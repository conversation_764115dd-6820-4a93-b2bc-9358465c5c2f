# 專案代辦事項管理網站 - 功能規劃

## 1. 專案概觀

本專案旨在開發一個純前端的代辦事項管理網站。使用者可以建立多個專案，並在每個專案下管理具有層級關係的任務與子任務。所有資料將儲存在瀏覽器的 LocalStorage 中，並可隨時匯出成 JSON 檔案。

---

## 2. 核心功能

### 2.1. 專案管理
- **建立專案**：使用者可以透過側邊抽屜介面新增專案。
- **選擇專案**：使用者可以從抽屜列表中點擊選擇一個當前要操作的專案。
- **刪除專案**：使用者可以刪除指定的專案（為簡化初期開發，此功能可設為次要優先級）。
- **介面**：專案列表將收納在一個預設隱藏的抽屜 (Drawer) 中，可透過漢堡選單按鈕開啟。

### 2.2. 任務與子任務管理
- **層級結構**：支援「主任務 -> 子任務」的二級結構。
- **新增**：
    - 可在當前專案下新增主任務。
    - 可在指定的主任務下新增子任務。
- **狀態管理**：
    - **待辦 (To-Do)**：任務的預設狀態。
    - **已完成 (Completed)**：使用者可透過勾選核取方塊將任務標記為已完成。
- **列表分離**：
    - 主任務列表將視覺上分為「待辦任務」和「已完成任務」兩個區塊。
    - 同樣地，每個主任務下的子任務列表也將分為「待辦子任務」和「已完成子任務」兩個區塊。
    - **結構完整性**：子任務永遠保持在其父任務的範疇內，不會因狀態改變而脫離。
- **取消完成**：使用者可以將「已完成」的任務取消勾選，使其狀態還原為「待辦」。

### 2.3. 進度統計與視覺化

- **任務完成百分比**：
    - 對於有子任務的主任務，系統會計算其下已完成子任務的比例 (例如 `3/5 已完成`)。
    - 這個統計資訊會顯示在主任務的標題旁邊。
- **專案完成百分比**：
    - 系統會計算整個專案中所有任務（包含主任務和子任務）的總完成度。
    - 百分比會以進度條 (Progress Bar) 的形式顯示在專案標題下方或抽屜的專案列表中，提供直觀的進度概覽。

### 2.4. 任務屬性與顯示
- **創建日期 (`creationDate`)**：
    - 任務或子任務在建立時，系統會自動記錄當下的日期與時間。
    - 此日期會顯示在列表中的任務項目上。
- **完成日期 (`completionDate`)**：
    - 當任務被標記為「完成」時，系統會記錄當下的日期與時間。
    - 當任務被「取消完成」時，此日期記錄將被清空 (`null`)。
    - 此日期會顯示在已完成列表的任務項目上。
- **展開與收折**：
    - 主任務列表項目應提供可點擊的區域（例如，任務文字本身）。
    - 點擊後可展開或收折其下的子任務列表，方便使用者聚焦。

### 2.5. AI 助理 (Gemini API)

- **互動介面**：
    - 頁面左側將提供一個固定的聊天視窗，讓使用者可以與 AI 持續對話。
    - 聊天視窗包含訊息顯示區、使用者輸入框和一個用於設定 Gemini API 金鑰的區域。
- **核心功能**：
    - **自然語言理解**：使用者可以用日常對話的方式，命令 AI 執行任務。
    - **智慧創建**：AI 能夠解析使用者指令，並自動在當前專案中執行以下操作：
        - 建立新專案 (例如：「幫我開一個『家庭旅遊』的新專案」)。
        - 建立新的主任務 (例如：「新增一個任務『訂機票』」)。
        - 建立新的子任務 (例如：「在『訂機票』下面，新增一個子任務『比價』」)。
- **API 金鑰管理**：
    - 應用程式需要使用者提供自己的 Gemini API 金鑰才能啟用 AI 功能。
    - **UI 狀態**：當 API 金鑰未設定時，聊天視窗應呈現為不可操作的「鎖定」狀態（例如：淡化、輸入框禁用）。
    - **使用者引導**：若使用者在鎖定狀態下試圖與聊天視窗互動，系統應跳出提示，並引導其至設定區塊。
    - **設定說明**：在設定區塊中，提供清晰的文字說明，解釋如何前往 Google AI Studio 取得 Gemini API 金鑰，並附上超連結。
    - 金鑰將與專案資料一同儲存、匯入及匯出。

### 2.6. 資料處理
- **本地儲存**：包含 `apiKey` 和 `projects` 的整個應用程式狀態物件，將以 JSON 格式儲存在瀏覽器的 `localStorage` 中。
- **資料匯出**：
    - 提供一個「匯出」按鈕。
    - 點擊後，系統會將完整的狀態物件（包含 API 金鑰）轉換為一個 `projects-backup.json` 檔案，並觸發瀏覽器下載。
- **資料匯入**：
    - 提供一個「匯入」按鈕，點擊後會觸發檔案選擇對話框。
    - 使用者選擇先前匯出的 JSON 檔案後，系統會讀取其內容，驗證其結構，並用其資料完全覆蓋當前的應用程式狀態。
    - 匯入成功後，應用程式會自動重新整理，以反映新的資料狀態（包括 API 金鑰和所有專案）。

---

## 3. 資料模型 (Data Structure)

應用程式的核心資料將遵循以下 JavaScript 物件結構。整個狀態物件將被儲存和匯出。

```json
{
  "apiKey": "g-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "projects": [
    {
      "id": "proj-1660000000000",
      "name": "網站開發專案",
      "tasks": [
        {
          "id": "task-1660000100000",
          "text": "規劃與設計",
          "completed": false,
          "expanded": true,
          "creationDate": "2025-08-06T10:00:00.000Z",
          "completionDate": null,
          "subtasks": []
        }
      ]
    }
  ]
}
```
