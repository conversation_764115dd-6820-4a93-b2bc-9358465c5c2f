目前系統為V4.2版本，上次任務是「請把卡片適配內容寬度及限制至最小寬度，讓任務卡片不要跟頁面一樣寬，TAB
  page請包涵任務看板及專案看板，目前頁面中的專案看板應該改名為任務看板，在新增一個新的看板作為專案看板，該專案
  看板可以顯示出目前共有多少專案執行中或完成，且可進行篩選執行中及已完成之專案，每個專案卡片都能顯示進度條及查
  看專案按鈕，點擊查看專案時，會跳轉至任務看板中，並顯示該專案的任務卡片，每個任務卡片應該能顯示執行中或已完成
  的子任務，且能夠篩選單一卡片的任務要顯示已完成還是執行中的子任務。每個子任務應該都要有創建日期及如果已點擊完
  成勾選後出現完成日期。請先更新.md檔案內容後，直接開始修正程式碼。」，但是執行中斷了，請幫我檢查目前專案程式
  碼是否有不完整部分，可以參考V4.2的@功能規劃_V4.2.md及@開發步驟_V4.2.md兩個檔案進行完整性的比對。請檢查完成後
  列出何處須修正及改善的。製作一份.md檔為改善策略文件。