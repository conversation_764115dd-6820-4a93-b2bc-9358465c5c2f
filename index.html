<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>專案代辦事項管理 V4.4</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Design System CSS -->
    <link rel="stylesheet" href="styles/design-system.css">
    <style>
        /* 簡單的滾動條樣式，使其不那麼突兀 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="flex h-screen bg-gray-100 font-sans">

    <!-- AI 聊天視窗 (固定在左側) -->
    <aside id="ai-chat-widget" class="w-80 flex flex-col bg-gray-200 p-4 border-r border-gray-300">
        <h2 class="text-xl font-bold text-center mb-4">AI 助理</h2>
        <div id="chat-history" class="flex-1 bg-white rounded-lg p-2 overflow-y-auto mb-4 border"></div>
        <form id="chat-form" class="flex">
            <input type="text" id="chat-input" class="flex-1 p-2 border rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="跟 AI 說話..." disabled>
            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-r-lg hover:bg-blue-600 disabled:bg-gray-400"><i class="fas fa-paper-plane"></i></button>
        </form>
    </aside>

    <!-- 主內容區 -->
    <div class="flex-1 flex flex-col">
        <!-- 分頁導航 -->
        <nav class="bg-white shadow-md">
            <div class="container mx-auto px-6">
                <div class="flex items-center justify-between">
                    <div class="flex">
                        <button data-tab="project-board" class="tab-btn py-4 px-6 block hover:text-blue-500 focus:outline-none">專案看板</button>
                        <button data-tab="task-board" class="tab-btn py-4 px-6 block hover:text-blue-500 focus:outline-none">任務看板</button>
                        <button data-tab="statistics" class="tab-btn py-4 px-6 block hover:text-blue-500 focus:outline-none">統計分析</button>
                        <button data-tab="settings" class="tab-btn py-4 px-6 block hover:text-blue-500 focus:outline-none">設定</button>
                    </div>
                    <!-- 搜尋和快速操作區域 -->
                    <div class="flex items-center space-x-3">
                        <!-- 快速操作按鈕 -->
                        <button id="quick-action-btn" class="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200" title="快速操作清單 (Alt+Q)">
                            <i class="fas fa-bolt text-sm"></i>
                            <span class="text-sm font-medium">快速操作</span>
                            <kbd class="px-1.5 py-0.5 text-xs bg-gray-100 border border-gray-300 rounded">Alt+Q</kbd>
                        </button>

                        <!-- 搜尋區域 -->
                        <div class="relative">
                            <div class="search-container relative">
                                <input type="text" id="global-search" class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="搜尋專案、任務...">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <div id="search-results" class="search-dropdown absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 hidden max-h-96 overflow-y-auto"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 分頁內容容器 -->
        <main class="flex-1 p-6 overflow-y-auto">
            <!-- 專案看板 (新增) -->
            <div id="project-board-tab-content" class="hidden">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">所有專案</h2>
                    <div id="project-filter-buttons" class="flex space-x-2 p-1 bg-gray-200 rounded-lg">
                        <button data-filter="all" class="px-3 py-1 text-sm rounded-md">全部</button>
                        <button data-filter="inprogress" class="px-3 py-1 text-sm rounded-md">執行中</button>
                        <button data-filter="completed" class="px-3 py-1 text-sm rounded-md">已完成</button>
                    </div>
                </div>
                <div id="project-card-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"></div>
            </div>

            <!-- 任務看板 (原 board-tab-content) -->
            <div id="task-board-tab-content">
                <!-- 頂部操作區 -->
                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center space-x-4">
                        <select id="project-selector" class="p-2 border rounded-lg"></select>
                        <form id="add-project-form" class="flex">
                            <input id="new-project-input" type="text" class="p-2 border rounded-l-lg" placeholder="新增專案..." required>
                            <button type="submit" class="bg-blue-500 text-white px-4 rounded-r-lg hover:bg-blue-600">+</button>
                        </form>
                    </div>
                </div>
                <!-- 進度條 -->
                <div id="project-progress-container" class="mb-6">
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-semibold text-gray-700">專案進度</span>
                        <span id="project-progress-text" class="text-sm font-semibold text-gray-700">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div id="project-progress-bar" class="bg-green-500 h-2.5 rounded-full transition-all duration-500" style="width: 0%"></div>
                    </div>
                </div>
                <!-- 批量操作工具列 -->
                <div id="bulk-operations-toolbar" class="hidden mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="selection-info text-sm text-blue-700">
                                已選擇 <span id="selection-count" class="font-semibold">0</span> 個項目
                            </div>
                            <button id="select-all-tasks" class="text-sm text-blue-600 hover:text-blue-800 underline">全選</button>
                            <button id="clear-selection" class="text-sm text-blue-600 hover:text-blue-800 underline">清除選擇</button>
                        </div>
                        <div class="bulk-actions flex items-center space-x-2">
                            <button id="bulk-complete" class="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600">
                                <i class="fas fa-check mr-1"></i>標記完成
                            </button>
                            <button id="bulk-move" class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600">
                                <i class="fas fa-arrows-alt mr-1"></i>移動到...
                            </button>
                            <button id="bulk-delete" class="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600">
                                <i class="fas fa-trash mr-1"></i>刪除
                            </button>
                            <button id="exit-bulk-mode" class="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600">
                                <i class="fas fa-times mr-1"></i>退出
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 新增主任務 -->
                <form id="add-task-form" class="flex mb-6">
                    <input id="new-task-input" type="text" class="flex-1 p-2 border rounded-l-lg" placeholder="新增一個主任務..." required>
                    <button type="submit" class="bg-blue-500 text-white px-4 rounded-r-lg hover:bg-blue-600">新增任務</button>
                </form>
                <!-- 任務列表 -->
                <div id="tasks-wrapper" class="space-y-6">
                    <div>
                        <h3 class="text-lg font-semibold mb-2 text-gray-800">待辦任務</h3>
                        <div id="todo-tasks-list" class="space-y-4"></div>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold mb-2 text-gray-800">已完成任務</h3>
                        <div id="completed-tasks-list" class="space-y-4"></div>
                    </div>
                </div>
            </div>

            <!-- 統計分析頁 -->
            <div id="statistics-tab-content" class="hidden">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">統計分析</h2>
                    <p class="text-gray-600">查看您的專案和任務完成情況統計</p>
                </div>

                <!-- 統計卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                                <i class="fas fa-tasks text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">總任務數</p>
                                <p id="total-tasks-stat" class="text-2xl font-bold text-gray-900">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-500">
                                <i class="fas fa-check-circle text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">已完成任務</p>
                                <p id="completed-tasks-stat" class="text-2xl font-bold text-gray-900">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-500">
                                <i class="fas fa-folder text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">總專案數</p>
                                <p id="total-projects-stat" class="text-2xl font-bold text-gray-900">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-500">
                                <i class="fas fa-clock text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">平均完成時間</p>
                                <p id="avg-completion-time-stat" class="text-2xl font-bold text-gray-900">-</p>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- 設定頁 -->
            <div id="settings-tab-content" class="hidden">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- API 金鑰設定卡片 -->
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-semibold mb-4">Gemini API 金鑰</h3>
                        <p class="text-gray-600 mb-4 text-sm">請至 <a href="https://aistudio.google.com/app/apikey" target="_blank" class="text-blue-500 hover:underline">Google AI Studio</a> 取得您的 Gemini API 金鑰以啟用 AI 助理功能。</p>
                        <div class="flex">
                            <input type="password" id="api-key-input" class="flex-1 p-2 border rounded-l-lg" placeholder="貼上您的 API 金鑰">
                            <button id="save-api-key-btn" class="bg-blue-500 text-white px-4 rounded-r-lg hover:bg-blue-600">儲存</button>
                        </div>
                    </div>
                    <!-- 資料管理卡片 -->
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-semibold mb-4">資料管理</h3>
                        <p class="text-gray-600 mb-4 text-sm">將您的所有專案、任務及設定匯出成一個 JSON 檔案進行備份，或從備份檔案中還原。</p>
                        <div class="flex space-x-4">
                            <button id="export-json-btn" class="w-full bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">匯出資料</button>
                            <label for="import-json-input" class="w-full bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 text-center cursor-pointer">匯入資料</label>
                            <input type="file" id="import-json-input" accept=".json" class="hidden">
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 通知和模態框容器 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>
    <div id="modal-container" class="fixed inset-0 z-40 hidden"></div>

    <!-- 載入組件庫 -->
    <script src="js/store/StateManager.js"></script>
    <script src="js/components/Button.js"></script>
    <script src="js/components/Toast.js"></script>
    <script src="js/components/Modal.js"></script>
    <script src="js/components/Loading.js"></script>
    <script src="js/components/SearchBox.js"></script>

    <!-- 載入模組 -->
    <script src="js/modules/SearchManager.js"></script>
    <script src="js/modules/SelectionManager.js"></script>
    <script src="js/modules/BulkOperationManager.js"></script>
    <script src="js/modules/StatisticsManager.js"></script>
    <script src="js/modules/ShortcutManager.js"></script>

    <!-- 主應用程式 -->
    <script src="app-v4.4.js"></script>
</body>
</html>
