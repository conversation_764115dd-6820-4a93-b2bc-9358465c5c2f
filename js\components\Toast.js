/**
 * Toast Component - V4.4
 * 通知系統組件，支援多種類型的通知
 */

class Toast {
    static container = null;
    static toasts = new Map();
    static nextId = 1;

    static init() {
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'toast-container';
            this.container.className = 'fixed top-4 right-4 z-50 space-y-2';
            document.body.appendChild(this.container);
        }
    }

    static show(message, type = 'info', options = {}) {
        this.init();

        const toastOptions = {
            id: this.nextId++,
            message,
            type,
            duration: options.duration || 3000,
            closable: options.closable !== false,
            action: options.action || null,
            persistent: options.persistent || false,
            ...options
        };

        const toastElement = this.createToastElement(toastOptions);
        this.container.appendChild(toastElement);
        this.toasts.set(toastOptions.id, toastElement);

        // 添加進入動畫
        requestAnimationFrame(() => {
            toastElement.classList.add('animate-fade-in');
        });

        // 自動移除（如果不是持久化的）
        if (!toastOptions.persistent && toastOptions.duration > 0) {
            setTimeout(() => {
                this.remove(toastOptions.id);
            }, toastOptions.duration);
        }

        return toastOptions.id;
    }

    static createToastElement(options) {
        const toast = document.createElement('div');
        toast.className = this.getToastClasses(options.type);
        toast.dataset.toastId = options.id;

        const icon = this.getIcon(options.type);
        const actionButton = options.action ? 
            `<button class="toast-action ml-3 text-sm font-medium underline hover:no-underline" data-action="toast-action">
                ${options.action.text}
            </button>` : '';

        const closeButton = options.closable ? 
            `<button class="toast-close ml-3 text-lg leading-none hover:opacity-70" data-action="toast-close">
                &times;
            </button>` : '';

        toast.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="${icon} text-lg"></i>
                </div>
                <div class="ml-3 flex-1">
                    <div class="toast-message text-sm font-medium">
                        ${options.message}
                    </div>
                    ${actionButton}
                </div>
                ${closeButton}
            </div>
        `;

        // 綁定事件
        toast.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            
            if (action === 'toast-close') {
                this.remove(options.id);
            } else if (action === 'toast-action' && options.action) {
                options.action.callback();
                if (options.action.closeOnAction !== false) {
                    this.remove(options.id);
                }
            }
        });

        return toast;
    }

    static getToastClasses(type) {
        const baseClasses = [
            'toast',
            'max-w-sm',
            'w-full',
            'p-4',
            'rounded-lg',
            'shadow-lg',
            'border',
            'cursor-pointer',
            'transition-all',
            'duration-300',
            'transform',
            'hover:scale-105'
        ];

        const typeClasses = {
            success: [
                'bg-green-50',
                'border-green-200',
                'text-green-800'
            ],
            error: [
                'bg-red-50',
                'border-red-200',
                'text-red-800'
            ],
            warning: [
                'bg-yellow-50',
                'border-yellow-200',
                'text-yellow-800'
            ],
            info: [
                'bg-blue-50',
                'border-blue-200',
                'text-blue-800'
            ]
        };

        return [...baseClasses, ...typeClasses[type]].join(' ');
    }

    static getIcon(type) {
        const icons = {
            success: 'fas fa-check-circle text-green-500',
            error: 'fas fa-exclamation-circle text-red-500',
            warning: 'fas fa-exclamation-triangle text-yellow-500',
            info: 'fas fa-info-circle text-blue-500'
        };

        return icons[type] || icons.info;
    }

    static remove(id) {
        const toast = this.toasts.get(id);
        if (toast) {
            // 添加退出動畫
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
                this.toasts.delete(id);
            }, 300);
        }
    }

    static clear() {
        this.toasts.forEach((toast, id) => {
            this.remove(id);
        });
    }

    // 便捷方法
    static success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    static error(message, options = {}) {
        return this.show(message, 'error', options);
    }

    static warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    static info(message, options = {}) {
        return this.show(message, 'info', options);
    }

    // 特殊類型的通知
    static loading(message, options = {}) {
        return this.show(
            `<i class="fas fa-spinner animate-spin mr-2"></i>${message}`,
            'info',
            { persistent: true, closable: false, ...options }
        );
    }

    static confirm(message, onConfirm, options = {}) {
        return this.show(message, 'warning', {
            persistent: true,
            action: {
                text: '確認',
                callback: onConfirm,
                closeOnAction: true
            },
            ...options
        });
    }

    // 進度通知
    static progress(message, progress = 0, options = {}) {
        const progressBar = `
            <div class="mt-2">
                <div class="bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                         style="width: ${progress}%"></div>
                </div>
                <div class="text-xs text-gray-600 mt-1">${progress}%</div>
            </div>
        `;

        return this.show(message + progressBar, 'info', {
            persistent: true,
            closable: false,
            ...options
        });
    }

    // 更新進度通知
    static updateProgress(id, progress) {
        const toast = this.toasts.get(id);
        if (toast) {
            const progressBar = toast.querySelector('.bg-blue-500');
            const progressText = toast.querySelector('.text-xs');
            
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
            }
            if (progressText) {
                progressText.textContent = `${progress}%`;
            }

            // 如果完成，自動關閉
            if (progress >= 100) {
                setTimeout(() => {
                    this.remove(id);
                }, 1000);
            }
        }
    }
}

// 導出組件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Toast;
}
