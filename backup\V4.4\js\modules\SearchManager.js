/**
 * SearchManager - V4.3
 * 智慧搜尋系統，支援多維度搜尋和即時結果
 */

class SearchManager {
    constructor(stateManager) {
        this.stateManager = stateManager;
        this.searchIndex = new Map();
        this.searchHistory = [];
        this.maxHistorySize = 20;
        this.debounceTimer = null;
        this.debounceDelay = 300;
        
        // 初始化搜尋索引
        this.buildSearchIndex();
        
        // 監聽狀態變化重建索引
        this.stateManager.subscribe((newState, oldState) => {
            if (newState.projects !== oldState.projects) {
                this.buildSearchIndex();
            }
        });
    }

    // 建立搜尋索引
    buildSearchIndex() {
        this.searchIndex.clear();
        const state = this.stateManager.getState();
        
        state.projects.forEach(project => {
            // 索引專案
            this.addToIndex('project', project.id, {
                type: 'project',
                id: project.id,
                title: project.name,
                content: project.description || '',
                tags: project.tags || [],
                creationDate: project.creationDate,
                searchableText: this.createSearchableText([
                    project.name,
                    project.description || '',
                    ...(project.tags || [])
                ])
            });

            // 索引任務
            project.tasks.forEach(task => {
                this.addToIndex('task', task.id, {
                    type: 'task',
                    id: task.id,
                    projectId: project.id,
                    projectName: project.name,
                    title: task.text,
                    content: task.description || '',
                    tags: task.tags || [],
                    priority: task.priority || 'medium',
                    completed: task.completed,
                    creationDate: task.creationDate,
                    searchableText: this.createSearchableText([
                        task.text,
                        task.description || '',
                        project.name,
                        ...(task.tags || [])
                    ])
                });

                // 索引子任務
                if (task.subtasks) {
                    task.subtasks.forEach(subtask => {
                        this.addToIndex('subtask', subtask.id, {
                            type: 'subtask',
                            id: subtask.id,
                            taskId: task.id,
                            projectId: project.id,
                            projectName: project.name,
                            taskName: task.text,
                            title: subtask.text,
                            completed: subtask.completed,
                            creationDate: subtask.creationDate,
                            searchableText: this.createSearchableText([
                                subtask.text,
                                task.text,
                                project.name
                            ])
                        });
                    });
                }
            });
        });
    }

    // 添加到索引
    addToIndex(type, id, data) {
        const key = `${type}-${id}`;
        this.searchIndex.set(key, data);
    }

    // 創建可搜尋文字
    createSearchableText(texts) {
        return texts
            .filter(text => text && typeof text === 'string')
            .join(' ')
            .toLowerCase()
            .trim();
    }

    // 執行搜尋
    search(query, options = {}) {
        const {
            types = ['project', 'task', 'subtask'],
            limit = 50,
            includeCompleted = true,
            sortBy = 'relevance' // relevance, date, alphabetical
        } = options;

        if (!query || query.trim().length < 1) {
            return [];
        }

        const normalizedQuery = query.toLowerCase().trim();
        const results = [];

        // 搜尋索引
        this.searchIndex.forEach((item, key) => {
            if (!types.includes(item.type)) return;
            if (!includeCompleted && item.completed) return;

            const score = this.calculateRelevanceScore(item, normalizedQuery);
            if (score > 0) {
                results.push({
                    ...item,
                    score,
                    highlights: this.getHighlights(item, normalizedQuery)
                });
            }
        });

        // 排序結果
        this.sortResults(results, sortBy);

        // 限制結果數量
        const limitedResults = results.slice(0, limit);

        // 保存搜尋歷史
        this.addToHistory(query);

        return limitedResults;
    }

    // 計算相關性分數
    calculateRelevanceScore(item, query) {
        let score = 0;
        const searchableText = item.searchableText;

        // 完全匹配標題
        if (item.title.toLowerCase() === query) {
            score += 100;
        }

        // 標題包含查詢
        if (item.title.toLowerCase().includes(query)) {
            score += 50;
        }

        // 標題開始匹配
        if (item.title.toLowerCase().startsWith(query)) {
            score += 30;
        }

        // 內容匹配
        if (searchableText.includes(query)) {
            score += 20;
        }

        // 標籤匹配
        if (item.tags && item.tags.some(tag => tag.toLowerCase().includes(query))) {
            score += 25;
        }

        // 優先級加權
        if (item.priority === 'high') {
            score += 5;
        }

        // 類型加權
        if (item.type === 'project') {
            score += 10;
        } else if (item.type === 'task') {
            score += 5;
        }

        // 最近創建的項目加權
        if (item.creationDate) {
            const daysSinceCreation = (Date.now() - new Date(item.creationDate).getTime()) / (1000 * 60 * 60 * 24);
            if (daysSinceCreation < 7) {
                score += 3;
            }
        }

        return score;
    }

    // 獲取高亮文字
    getHighlights(item, query) {
        const highlights = [];
        
        // 高亮標題
        if (item.title.toLowerCase().includes(query)) {
            highlights.push({
                field: 'title',
                text: this.highlightText(item.title, query)
            });
        }

        // 高亮內容
        if (item.content && item.content.toLowerCase().includes(query)) {
            highlights.push({
                field: 'content',
                text: this.highlightText(item.content, query, 100)
            });
        }

        return highlights;
    }

    // 高亮文字
    highlightText(text, query, maxLength = null) {
        const regex = new RegExp(`(${this.escapeRegex(query)})`, 'gi');
        let highlighted = text.replace(regex, '<mark>$1</mark>');
        
        if (maxLength && highlighted.length > maxLength) {
            const queryIndex = highlighted.toLowerCase().indexOf(query.toLowerCase());
            const start = Math.max(0, queryIndex - 50);
            const end = Math.min(highlighted.length, start + maxLength);
            highlighted = (start > 0 ? '...' : '') + 
                         highlighted.substring(start, end) + 
                         (end < highlighted.length ? '...' : '');
        }
        
        return highlighted;
    }

    // 轉義正則表達式特殊字符
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // 排序結果
    sortResults(results, sortBy) {
        switch (sortBy) {
            case 'relevance':
                results.sort((a, b) => b.score - a.score);
                break;
            case 'date':
                results.sort((a, b) => new Date(b.creationDate) - new Date(a.creationDate));
                break;
            case 'alphabetical':
                results.sort((a, b) => a.title.localeCompare(b.title));
                break;
        }
    }

    // 添加到搜尋歷史
    addToHistory(query) {
        const trimmedQuery = query.trim();
        if (!trimmedQuery || this.searchHistory.includes(trimmedQuery)) {
            return;
        }

        this.searchHistory.unshift(trimmedQuery);
        
        // 限制歷史記錄大小
        if (this.searchHistory.length > this.maxHistorySize) {
            this.searchHistory = this.searchHistory.slice(0, this.maxHistorySize);
        }

        // 更新狀態
        this.stateManager.setState({
            searchHistory: [...this.searchHistory]
        }, { silent: true });
    }

    // 獲取搜尋歷史
    getSearchHistory() {
        return [...this.searchHistory];
    }

    // 清除搜尋歷史
    clearSearchHistory() {
        this.searchHistory = [];
        this.stateManager.setState({
            searchHistory: []
        }, { silent: true });
    }

    // 獲取搜尋建議
    getSuggestions(query, limit = 5) {
        if (!query || query.length < 2) {
            return this.searchHistory.slice(0, limit);
        }

        const suggestions = new Set();
        const normalizedQuery = query.toLowerCase();

        // 從搜尋歷史中獲取建議
        this.searchHistory.forEach(historyItem => {
            if (historyItem.toLowerCase().includes(normalizedQuery)) {
                suggestions.add(historyItem);
            }
        });

        // 從索引中獲取建議
        this.searchIndex.forEach(item => {
            if (suggestions.size >= limit) return;
            
            if (item.title.toLowerCase().includes(normalizedQuery)) {
                suggestions.add(item.title);
            }
            
            if (item.tags) {
                item.tags.forEach(tag => {
                    if (tag.toLowerCase().includes(normalizedQuery)) {
                        suggestions.add(tag);
                    }
                });
            }
        });

        return Array.from(suggestions).slice(0, limit);
    }

    // 防抖搜尋
    debouncedSearch(query, callback, options = {}) {
        clearTimeout(this.debounceTimer);
        
        this.debounceTimer = setTimeout(() => {
            const results = this.search(query, options);
            callback(results);
        }, this.debounceDelay);
    }

    // 取消防抖搜尋
    cancelDebouncedSearch() {
        clearTimeout(this.debounceTimer);
    }

    // 獲取統計資訊
    getSearchStats() {
        const totalItems = this.searchIndex.size;
        const itemsByType = {};
        
        this.searchIndex.forEach(item => {
            itemsByType[item.type] = (itemsByType[item.type] || 0) + 1;
        });

        return {
            totalItems,
            itemsByType,
            historySize: this.searchHistory.length
        };
    }
}

// 導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchManager;
} else {
    window.SearchManager = SearchManager;
}
