# 專案代辦事項管理 - V3 開發步驟

## 總覽

本指南旨在將 V3 的功能規劃轉化為一個採用 Tailwind CSS 和分頁式介面的現代化 Web 應用。開發流程將確保結構清晰、樣式統一且功能穩定。

---

## 階段一：HTML 結構與 Tailwind CSS 設定

**目標**：建立符合分頁佈局的 HTML 骨架，並引入 Tailwind CSS。

1.  **檔案結構**：
    - `index.html`
    - `app.js`
    - (注意：不再需要 `style.css` 檔案)

2.  **`index.html` 設定**：
    - 在 `<head>` 中，引入 Tailwind CSS 的 CDN 腳本：
      ```html
      <script src="https://cdn.tailwindcss.com"></script>
      ```
    - **主體佈局**：使用 Flexbox (`<body class="flex">`) 將頁面分為左側的 AI 助理 (`<aside>`) 和右側的主內容區 (`<div class="flex-grow">`)。
    - **分頁 (Tabs) 結構**：
        - 在主內容區頂部，建立分頁按鈕 (`<nav>`)，包含「專案看板」和「設定」兩個按鈕。
        - 在按鈕下方，建立兩個對應的內容容器 (`<div>`)，一個用於看板 (`#board-tab-content`)，另一個用於設定 (`#settings-tab-content`)。預設只顯示看板內容。
    - **專案看板 (`#board-tab-content`)**：
        - 包含專案選擇的下拉選單 (`<select id="project-selector">`)。
        - 包含專案進度條。
        - 包含「待辦」和「已完成」兩個任務列表容器。
    - **設定頁 (`#settings-tab-content`)**：
        - 包含 API 金鑰設定的卡片。
        - 包含資料匯入/匯出的卡片。

---

## 階段二：JavaScript 核心邏輯

**目標**：實現應用程式的狀態管理、核心功能和 DOM 更新邏輯。

1.  **狀態管理**：
    - 沿用 V2 的 `state` 物件結構。
    - 建立 `saveState()` 和 `loadState()` 函式與 `localStorage` 互動。

2.  **渲染引擎 (`Render Engine`)**：
    - 建立一個主 `render()` 函式，它將成為所有 UI 更新的統一入口。
    - `render()` 函式會根據 `state` 的資料，呼叫不同的子渲染函式：
        - `renderTabs()`: 根據當前活動的分頁，顯示或隱藏對應的內容 `<div>`。
        - `renderProjectSelector()`: 更新專案下拉選單的選項。
        - `renderBoard()`: 渲染整個「專案看板」的內容，包括進度條和任務卡片。
        - `renderSettings()`: 更新設定頁中的 API 金鑰輸入框。
        - `renderAIChat()`: 根據 API 金鑰是否存在，鎖定或解鎖 AI 聊天視窗。

3.  **DOM 元素創建**：
    - 建立 `createTaskCard(task)` 函式。此函式將使用 Tailwind CSS 的 class 來動態生成一個完整的任務卡片 `<div>`。卡片內部會包含所有必要的資訊（標題、日期、子任務列表等）。

4.  **核心功能**：
    - 建立 `addProject()`, `addTask()`, `toggleTaskCompletion()` 等函式。這些函式**只修改 `state`**，然後呼叫 `render()` 來更新 UI。

---

## 階段三：事件監聽與整合

**目標**：將使用者的操作與核心邏輯連接起來，並整合外部服務。

1.  **分頁切換**：
    - 監聽分頁按鈕的點擊事件，更新一個用於追蹤當前分頁的狀態變數，然後呼叫 `render()`。

2.  **專案選擇**：
    - 監聽專案下拉選單的 `change` 事件，更新 `state.currentProjectId`，然後呼叫 `render()`。

3.  **任務互動 (事件委派)**：
    - 在任務列表的父容器上設定事件監聽器。
    - 根據被點擊元素的 `class` 或 `data-` 屬性，判斷使用者意圖（例如，點擊了完成核取方塊、展開按鈕等），然後呼叫對應的核心功能函式並重新渲染。

4.  **設定頁互動**：
    - 監聽「儲存 API 金鑰」按鈕的點擊，更新 `state.apiKey`。
    - 監聽匯入/匯出按鈕，實現與檔案系統的互動。

5.  **AI 助理整合 (真實 API - 智慧規劃)**：
    - **API 呼叫**：
        - 建立 `async function callGeminiAPI(prompt)` 函式，負責與 Gemini API 進行 `fetch` 通訊。
    - **提示工程 (Prompt Engineering)**：
        - 這是此功能的成敗關鍵。我們需要設計一個能讓 AI 在兩種模式下運作的提示：
        - **模式一：快速新增**。提示詞引導 AI 回傳單一動作 JSON。範例：「...若使用者指令單純，回傳單一動作 JSON... 使用者說：『新增任務：買牛奶』。你回傳：`{"action": "addTask", "payload": {"text": "買牛奶"}}`」
        - **模式二：完整規劃**。提示詞引導 AI 回傳一個完整的專案物件 JSON。範例：「...若使用者要求規劃，請回傳一個符合我們應用程式資料結構的完整專案物件 JSON。專案物件應包含 `id`, `name`, 和一個 `tasks` 陣列... 使用者說：『規劃一個為期一週的東京旅遊』。你回傳：`{"action": "addProjectPlan", "payload": {"id": "proj-xxxx", "name": "東京一週旅遊", "tasks": [...]}}`」
    - **回應處理與動作分派**：
        - 接收到 Gemini API 的回應後，解析其 JSON 內容，得到一個包含 `action` 和 `payload` 的指令物件。
        - 建立一個 `executeAIAction(command)` 函式，它將作為 AI 指令的總控制器。
        - 使用 `switch (command.action)` 來分派任務：
            - `case 'addTask'`: 呼叫 `addTask()` 函式，將 `command.payload` 中的任務新增到**當前**專案。
            - `case 'addProjectPlan'`: 直接將 `command.payload` (這是一個完整的專案物件) `push` 到 `state.projects` 陣列中。這是最核心的變更。
            - 其他可能的 `case`...
    - **UI 更新**：
        - 在 `executeAIAction` 函式執行完畢後，呼叫主 `render()` 函式，UI 將自動更新以反映新增的任務或整個新專案。
