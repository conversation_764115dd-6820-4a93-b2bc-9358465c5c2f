/**
 * StateManager - V4.3
 * 集中式狀態管理器，支援狀態訂閱和持久化
 */

class StateManager {
    constructor() {
        this.state = this.getInitialState();
        this.listeners = new Map();
        this.history = [];
        this.maxHistorySize = 50;
        this.storageKey = 'project-todo-app-state-v4.3';
        
        // 載入保存的狀態
        this.loadState();
        
        // 初始化UI狀態
        this.initUIState();
    }

    getInitialState() {
        return {
            // 基本設定
            apiKey: null,
            currentProjectId: null,
            activeTab: 'project-board',
            
            // 篩選狀態
            projectBoardFilter: 'all',
            
            // 搜尋狀態
            searchQuery: '',
            searchHistory: [],
            searchResults: [],
            
            // 用戶偏好設定
            userPreferences: {
                theme: 'light',
                language: 'zh-TW',
                notifications: true,
                autoSave: true,
                shortcuts: {
                    newTask: 'Ctrl+N',
                    search: 'Ctrl+F',
                    selectAll: 'Ctrl+A'
                }
            },
            
            // 統計資料
            statistics: {
                totalTasksCompleted: 0,
                totalProjectsCompleted: 0,
                averageCompletionTime: 0,
                lastActiveDate: new Date().toISOString(),
                dailyStats: {},
                weeklyStats: {},
                monthlyStats: {}
            },
            
            // 專案資料
            projects: [],
            
            // UI狀態（不持久化）
            ui: {
                selectedTasks: new Set(),
                bulkOperationMode: false,
                currentModal: null,
                notifications: [],
                loading: false,
                searchDropdownOpen: false,
                sidebarCollapsed: false
            }
        };
    }

    initUIState() {
        // UI狀態不從localStorage載入，每次都重新初始化
        this.state.ui = {
            selectedTasks: new Set(),
            bulkOperationMode: false,
            currentModal: null,
            notifications: [],
            loading: false,
            searchDropdownOpen: false,
            sidebarCollapsed: false
        };
    }

    // 設置狀態
    setState(newState, options = {}) {
        const { 
            silent = false, 
            saveHistory = true, 
            persist = true 
        } = options;

        // 保存歷史記錄
        if (saveHistory) {
            this.saveToHistory();
        }

        // 深度合併狀態
        const oldState = this.deepClone(this.state);
        this.state = this.deepMerge(this.state, newState);

        // 持久化狀態（排除UI狀態）
        if (persist) {
            this.saveState();
        }

        // 通知監聽器
        if (!silent) {
            this.notifyListeners(oldState, this.state);
        }

        return this.state;
    }

    // 獲取狀態
    getState() {
        return this.deepClone(this.state);
    }

    // 獲取特定路徑的狀態
    getStateByPath(path) {
        return this.getNestedValue(this.state, path);
    }

    // 設置特定路徑的狀態
    setStateByPath(path, value, options = {}) {
        const newState = this.setNestedValue({}, path, value);
        return this.setState(newState, options);
    }

    // 訂閱狀態變化
    subscribe(listener, path = null) {
        const id = Date.now() + Math.random();
        this.listeners.set(id, { listener, path });
        
        // 返回取消訂閱函數
        return () => {
            this.listeners.delete(id);
        };
    }

    // 通知監聽器
    notifyListeners(oldState, newState) {
        this.listeners.forEach(({ listener, path }) => {
            if (path) {
                const oldValue = this.getNestedValue(oldState, path);
                const newValue = this.getNestedValue(newState, path);
                
                if (oldValue !== newValue) {
                    listener(newValue, oldValue, path);
                }
            } else {
                listener(newState, oldState);
            }
        });
    }

    // 保存到歷史記錄
    saveToHistory() {
        this.history.push(this.deepClone(this.state));
        
        // 限制歷史記錄大小
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
        }
    }

    // 撤銷操作
    undo() {
        if (this.history.length > 0) {
            const previousState = this.history.pop();
            this.state = previousState;
            this.saveState();
            this.notifyListeners({}, this.state);
            return true;
        }
        return false;
    }

    // 清除歷史記錄
    clearHistory() {
        this.history = [];
    }

    // 持久化狀態
    saveState() {
        try {
            // 排除UI狀態進行持久化
            const stateToSave = { ...this.state };
            delete stateToSave.ui;
            
            localStorage.setItem(this.storageKey, JSON.stringify(stateToSave));
        } catch (error) {
            console.error('Failed to save state:', error);
        }
    }

    // 載入狀態
    loadState() {
        try {
            const savedState = localStorage.getItem(this.storageKey);
            if (savedState) {
                const parsedState = JSON.parse(savedState);
                
                // 合併載入的狀態，保持UI狀態為初始值
                this.state = this.deepMerge(this.state, parsedState);
                
                // 重新初始化UI狀態
                this.initUIState();
                
                return true;
            }
        } catch (error) {
            console.error('Failed to load state:', error);
        }
        return false;
    }

    // 清除持久化狀態
    clearState() {
        localStorage.removeItem(this.storageKey);
        this.state = this.getInitialState();
        this.clearHistory();
        this.notifyListeners({}, this.state);
    }

    // 匯出狀態
    exportState() {
        const stateToExport = { ...this.state };
        delete stateToExport.ui;
        return JSON.stringify(stateToExport, null, 2);
    }

    // 匯入狀態
    importState(stateData) {
        try {
            const importedState = JSON.parse(stateData);
            
            // 驗證狀態結構
            if (this.validateState(importedState)) {
                this.setState(importedState, { saveHistory: false });
                return true;
            } else {
                throw new Error('Invalid state structure');
            }
        } catch (error) {
            console.error('Failed to import state:', error);
            return false;
        }
    }

    // 驗證狀態結構
    validateState(state) {
        const requiredFields = ['projects', 'userPreferences', 'statistics'];
        return requiredFields.every(field => state.hasOwnProperty(field));
    }

    // 工具方法：深度克隆
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj);
        if (obj instanceof Set) return new Set(obj);
        if (obj instanceof Map) return new Map(obj);
        if (Array.isArray(obj)) return obj.map(item => this.deepClone(item));
        
        const cloned = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = this.deepClone(obj[key]);
            }
        }
        return cloned;
    }

    // 工具方法：深度合併
    deepMerge(target, source) {
        const result = this.deepClone(target);
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    result[key] = this.deepMerge(result[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    }

    // 工具方法：獲取嵌套值
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    // 工具方法：設置嵌套值
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        
        const target = keys.reduce((current, key) => {
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            return current[key];
        }, obj);
        
        target[lastKey] = value;
        return obj;
    }

    // 調試方法
    debug() {
        console.group('StateManager Debug Info');
        console.log('Current State:', this.state);
        console.log('Listeners Count:', this.listeners.size);
        console.log('History Length:', this.history.length);
        console.groupEnd();
    }
}

// 創建全域狀態管理器實例
const stateManager = new StateManager();

// 導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { StateManager, stateManager };
} else {
    window.StateManager = StateManager;
    window.stateManager = stateManager;
}
