# 專案代辦事項管理 - V2 功能規劃

## 1. 專案願景 (Project Vision)

打造一個直觀、高效且智慧化的個人專案管理工具。使用者不僅能輕鬆管理複雜的任務層級，還能透過 AI 助理簡化創建流程，並透過視覺化的進度追蹤，始終對專案全貌保持清晰的掌控。資料的可攜性（匯入/匯出）確保使用者對其資料擁有完全的所有權。

---

## 2. 核心使用者故事 (Core User Stories)

### 故事一：基礎任務管理
**作為一個使用者，我想要：**
- 建立不同的專案，以便區分我的工作和個人事務。
- 在專案中新增、編輯和刪除主任務。
- 為主任務添加子任務，以拆解複雜的工作。
- 勾選來完成任務或子任務，並能清晰地看到它們的創建和完成日期。
- 將已完成的任務移至單獨的「已完成」清單，同時保持其原有的父子關係，以便回顧。
- 能夠展開或收合主任務，以聚焦於當前重要的事項。

### 故事二：進度視覺化
**作為一個使用者，我想要：**
- 在每個主任務旁邊看到其子任務的完成情況統計（例如 `(3/5)`），以便快速評估工作量。
- 在專案主畫面上看到一個總體的進度條和百分比，以便一目了然地了解整個專案的健康狀況。

### 故事三：AI 智慧助理
**作為一個使用者，我想要：**
- 透過一個聊天視窗，用自然語言（例如「新增任務：設計首頁」）快速創建專案和任務，而無需手動點擊和輸入。
- 在我沒有設定 API 金鑰時，AI 助理功能應被鎖定並提示我進行設定，同時提供清晰的指引告訴我如何獲取金鑰。

### 故事四：資料可攜性
**作為一個使用者，我想要：**
- 將我所有的專案、任務和設定（包含 API 金鑰）匯出成一個單一的 JSON 檔案，以便備份或轉移到另一台裝置。
- 能夠從備份的 JSON 檔案中匯入所有資料，並立即恢復我的工作環境。

---

## 3. 技術規格 (Technical Specifications)

- **前端框架**：無 (使用純 HTML, CSS, JavaScript)。
- **核心 API**：Google Gemini API (用於 AI 助理功能)。
- **資料儲存**：瀏覽器的 `localStorage`，作為應用程式狀態的「單一事實來源 (Single Source of Truth)」。

---

## 4. 資料結構藍圖 (Data Schema Blueprint)

整個應用程式的狀態將儲存在一個根物件中，其結構如下：

```json
{
  // 用於驅動 AI 助理功能，可為 null
  "apiKey": "g-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",

  // 當前正在檢視的專案 ID
  "currentProjectId": "proj-1660000000000",

  // 包含所有專案的陣列
  "projects": [
    {
      "id": "proj-1660000000000",
      "name": "網站開發專案",
      "tasks": [
        {
          "id": "task-1660000100000",
          "text": "規劃與設計",
          "completed": false,         // 標記主任務是否完成
          "expanded": true,           // 控制子任務列表的顯示/隱藏
          "creationDate": "2025-08-06T10:00:00.000Z",
          "completionDate": null,     // 完成時的時間戳，否則為 null
          "subtasks": [
            {
              "id": "sub-1660000200000",
              "text": "繪製線框圖",
              "completed": true,
              "creationDate": "2025-08-06T11:00:00.000Z",
              "completionDate": "2025-08-07T14:00:00.000Z"
            }
          ]
        }
      ]
    }
  ]
}
```
