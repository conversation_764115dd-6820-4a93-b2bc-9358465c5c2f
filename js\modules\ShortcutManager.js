/**
 * ShortcutManager - V4.4
 * 鍵盤快捷鍵管理器
 */

class ShortcutManager {
    constructor(stateManager) {
        this.stateManager = stateManager;
        this.shortcuts = new Map();
        this.isEnabled = true;
        this.activeModals = 0;
        
        this.init();
    }

    init() {
        this.registerDefaultShortcuts();
        this.bindEvents();
    }

    // 註冊預設快捷鍵
    registerDefaultShortcuts() {
        // 快速清單（主要快捷鍵）
        this.register('Alt+Q', (e) => {
            e.preventDefault();
            this.showQuickActionList();
        }, '開啟快速操作清單');

        // 搜尋（使用Alt避免衝突）
        this.register('Alt+F', (e) => {
            e.preventDefault();
            // 如果當前在其他輸入框中，先失去焦點
            if (document.activeElement && this.isInputElement(document.activeElement)) {
                document.activeElement.blur();
            }
            const searchInput = document.getElementById('global-search');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }, '開啟搜尋');

        // 新增任務
        this.register('Alt+N', (e) => {
            e.preventDefault();
            const state = this.stateManager.getState();
            if (state.activeTab === 'task-board' && state.currentProjectId) {
                const taskInput = document.getElementById('new-task-input');
                if (taskInput) {
                    taskInput.focus();
                }
            } else {
                Toast.info('請先選擇一個專案');
            }
        }, '新增任務');

        // 全選（僅在批量模式下）
        this.register('Alt+A', (e) => {
            const state = this.stateManager.getState();
            if (state.activeTab === 'task-board' && state.ui.bulkOperationMode) {
                e.preventDefault();
                document.dispatchEvent(new CustomEvent('selectAllTasks'));
            }
        }, '全選任務（批量模式）');

        // 切換分頁（使用Alt避免衝突）
        this.register('Alt+1', (e) => {
            e.preventDefault();
            this.switchTab('project-board');
        }, '切換到專案看板');

        this.register('Alt+2', (e) => {
            e.preventDefault();
            this.switchTab('task-board');
        }, '切換到任務看板');

        this.register('Alt+3', (e) => {
            e.preventDefault();
            this.switchTab('statistics');
        }, '切換到統計分析');

        this.register('Alt+4', (e) => {
            e.preventDefault();
            this.switchTab('settings');
        }, '切換到設定');

        // 批量操作（使用Alt+Shift避免衝突）
        this.register('Alt+Shift+C', (e) => {
            e.preventDefault();
            const state = this.stateManager.getState();
            if (state.ui.bulkOperationMode) {
                document.dispatchEvent(new CustomEvent('bulkCompleteTasks'));
            }
        }, '批量完成任務');

        this.register('Alt+Shift+M', (e) => {
            e.preventDefault();
            const state = this.stateManager.getState();
            if (state.ui.bulkOperationMode) {
                document.dispatchEvent(new CustomEvent('bulkMoveTasks'));
            }
        }, '批量移動任務');

        this.register('Alt+Shift+D', (e) => {
            e.preventDefault();
            const state = this.stateManager.getState();
            if (state.ui.bulkOperationMode && state.ui.selectedTasks.size > 0) {
                document.dispatchEvent(new CustomEvent('bulkDeleteTasks'));
            }
        }, '批量刪除任務');

        // 進入/退出選擇模式
        this.register('Alt+S', (e) => {
            e.preventDefault();
            document.dispatchEvent(new CustomEvent('toggleSelectionMode'));
        }, '切換選擇模式');

        // 幫助
        this.register('F1', (e) => {
            e.preventDefault();
            this.showHelpModal();
        }, '顯示幫助');

        // ESC - 通用取消操作
        this.register('Escape', () => {
            const state = this.stateManager.getState();
            
            // 如果有模態框，關閉模態框
            if (this.activeModals > 0) {
                return; // 讓模態框自己處理
            }
            
            // 如果在選擇模式，退出選擇模式
            if (state.ui.bulkOperationMode) {
                document.dispatchEvent(new CustomEvent('exitSelectionMode'));
                return;
            }
            
            // 如果搜尋框有焦點，清除搜尋
            const searchInput = document.getElementById('global-search');
            if (searchInput && document.activeElement === searchInput) {
                searchInput.blur();
                return;
            }
        }, '取消/退出');

        // 重新整理
        this.register('F5', (e) => {
            e.preventDefault();
            this.refreshCurrentView();
        }, '重新整理');

        // 匯出資料
        this.register('Alt+E', (e) => {
            e.preventDefault();
            document.dispatchEvent(new CustomEvent('exportData'));
        }, '匯出資料');
    }

    // 註冊快捷鍵
    register(keyCombo, callback, description = '') {
        const normalizedCombo = this.normalizeKeyCombo(keyCombo);
        this.shortcuts.set(normalizedCombo, {
            callback,
            description,
            originalCombo: keyCombo
        });
    }

    // 取消註冊快捷鍵
    unregister(keyCombo) {
        const normalizedCombo = this.normalizeKeyCombo(keyCombo);
        this.shortcuts.delete(normalizedCombo);
    }

    // 標準化按鍵組合
    normalizeKeyCombo(keyCombo) {
        return keyCombo
            .toLowerCase()
            .split('+')
            .map(key => key.trim())
            .sort((a, b) => {
                // 修飾鍵排序：ctrl, alt, shift, 然後是其他鍵
                const order = { ctrl: 1, alt: 2, shift: 3 };
                const aOrder = order[a] || 999;
                const bOrder = order[b] || 999;
                return aOrder - bOrder;
            })
            .join('+');
    }

    // 綁定事件
    bindEvents() {
        document.addEventListener('keydown', (e) => {
            if (!this.isEnabled) return;

            const keyCombo = this.getKeyCombo(e);

            // 檢查是否應該忽略這個快捷鍵
            if (this.shouldIgnoreShortcut(e, keyCombo)) {
                return;
            }

            const shortcut = this.shortcuts.get(keyCombo);

            if (shortcut) {
                try {
                    shortcut.callback(e);
                } catch (error) {
                    console.error('Shortcut execution error:', error);
                }
            }
        });

        // 監聽模態框狀態
        document.addEventListener('modalOpen', () => {
            this.activeModals++;
        });

        document.addEventListener('modalClose', () => {
            this.activeModals = Math.max(0, this.activeModals - 1);
        });
    }

    // 獲取按鍵組合
    getKeyCombo(event) {
        const keys = [];
        
        if (event.ctrlKey) keys.push('ctrl');
        if (event.altKey) keys.push('alt');
        if (event.shiftKey) keys.push('shift');
        
        const key = event.key ? event.key.toLowerCase() : '';
        
        // 特殊鍵映射
        const keyMap = {
            ' ': 'space',
            'arrowup': 'up',
            'arrowdown': 'down',
            'arrowleft': 'left',
            'arrowright': 'right'
        };
        
        keys.push(keyMap[key] || key);
        
        return keys.join('+');
    }

    // 檢查是否為輸入元素
    isInputElement(element) {
        const inputTypes = ['input', 'textarea', 'select'];
        const tagName = element.tagName.toLowerCase();

        return inputTypes.includes(tagName) ||
               element.contentEditable === 'true' ||
               element.isContentEditable;
    }

    // 檢查是否應該忽略快捷鍵
    shouldIgnoreShortcut(event, keyCombo) {
        // 如果在輸入元素中，只允許特定的快捷鍵
        if (this.isInputElement(event.target)) {
            const allowedInInput = [
                'escape',     // ESC - 取消輸入
                'f1',         // F1 - 幫助
                'alt+q'       // Alt+Q - 快速操作（在任何地方都可用）
            ];
            return !allowedInInput.includes(keyCombo);
        }

        // 如果是表單提交相關的按鍵，不要干擾
        if (event.key === 'Enter' && event.target.closest('form')) {
            return true;
        }

        return false;
    }

    // 切換分頁
    switchTab(tabName) {
        const tabButton = document.querySelector(`[data-tab="${tabName}"]`);
        if (tabButton) {
            tabButton.click();
        }
    }

    // 重新整理當前視圖
    refreshCurrentView() {
        const state = this.stateManager.getState();

        // 清空AI助理對話框
        this.clearAIChatHistory();

        switch (state.activeTab) {
            case 'project-board':
                document.dispatchEvent(new CustomEvent('refreshProjectBoard'));
                break;
            case 'task-board':
                document.dispatchEvent(new CustomEvent('refreshTaskBoard'));
                break;
            case 'statistics':
                document.dispatchEvent(new CustomEvent('refreshStatistics'));
                break;
        }

        Toast.info('頁面已重新整理，AI助理對話內容已清空');
    }

    // 清空AI助理對話歷史
    clearAIChatHistory() {
        const chatHistory = document.getElementById('chat-history');
        if (chatHistory) {
            chatHistory.innerHTML = '';

            // 檢查是否有API KEY來決定顯示的訊息
            const state = this.stateManager.getState();
            const isLocked = !state.apiKey;

            // 添加適當的歡迎訊息
            const welcomeMessage = document.createElement('div');
            welcomeMessage.className = 'flex items-start space-x-3 mb-4';

            if (isLocked) {
                // 沒有API KEY時的提示
                welcomeMessage.innerHTML = `
                    <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-key text-white text-sm"></i>
                    </div>
                    <div class="flex-1 bg-orange-50 border border-orange-200 rounded-lg p-3">
                        <div class="text-sm text-orange-800">
                            <div class="font-semibold mb-2">🔑 需要設定 API 金鑰</div>
                            <div class="mb-2">要使用 AI 助理功能，請先設定您的 Gemini API 金鑰：</div>
                            <div class="space-y-1 text-xs">
                                <div>1. 點擊右上角的 <i class="fas fa-cog"></i> 設定按鈕</div>
                                <div>2. 前往 <a href="https://aistudio.google.com/app/apikey" target="_blank" class="text-blue-600 hover:underline">Google AI Studio</a> 取得免費 API 金鑰</div>
                                <div>3. 將 API 金鑰貼到設定頁面並儲存</div>
                            </div>
                            <div class="mt-2 text-xs text-orange-600">
                                💡 Gemini API 提供免費額度，足夠日常使用
                            </div>
                        </div>
                    </div>
                `;
            } else {
                // 有API KEY時的歡迎訊息
                welcomeMessage.innerHTML = `
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-robot text-white text-sm"></i>
                    </div>
                    <div class="flex-1 bg-gray-100 rounded-lg p-3">
                        <div class="text-sm text-gray-700">
                            👋 您好！我是您的AI任務管理助理。<br>
                            您可以告訴我：<br>
                            • 「新增任務：買牛奶」<br>
                            • 「規劃一個週末旅遊行程」<br>
                            • 「幫我整理工作任務」<br><br>
                            我會幫您智慧地管理任務和專案！
                        </div>
                    </div>
                `;
            }

            chatHistory.appendChild(welcomeMessage);
        }
    }

    // 顯示快速操作清單
    showQuickActionList() {
        const state = this.stateManager.getState();
        const actions = this.getAvailableActions(state);

        const actionList = actions.map((action, index) => `
            <div class="quick-action-item p-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 flex items-center justify-between"
                 data-action="${action.key}" data-index="${index}">
                <div class="flex items-center space-x-3">
                    <i class="${action.icon} text-blue-500 w-5"></i>
                    <div>
                        <div class="font-medium text-gray-900">${action.title}</div>
                        <div class="text-sm text-gray-500">${action.description}</div>
                    </div>
                </div>
                <kbd class="px-2 py-1 text-xs font-mono bg-gray-100 border border-gray-300 rounded">${action.shortcut}</kbd>
            </div>
        `).join('');

        const content = `
            <div class="max-h-96 overflow-y-auto">
                <p class="text-gray-600 mb-4">選擇要執行的操作：</p>
                <div class="quick-action-list">
                    ${actionList}
                </div>
                <div class="mt-4 text-xs text-gray-500 text-center">
                    使用 ↑↓ 鍵選擇，Enter 執行，ESC 關閉
                </div>
            </div>
        `;

        const modal = new Modal({
            title: '快速操作清單 (Alt+Q)',
            content,
            size: 'lg',
            closable: true,
            closeOnBackdrop: true,
            closeOnEscape: true,
            showFooter: false,
            onOpen: () => {
                // 使用setTimeout確保modal已經完全初始化
                setTimeout(() => {
                    this.bindQuickActionEvents(modal);
                }, 0);
            }
        });

        modal.open();
    }

    // 獲取可用操作
    getAvailableActions(state) {
        const actions = [
            {
                key: 'search',
                title: '搜尋',
                description: '搜尋專案、任務或子任務',
                icon: 'fas fa-search',
                shortcut: 'Alt+F',
                available: true
            },
            {
                key: 'newTask',
                title: '新增任務',
                description: '在當前專案中新增任務',
                icon: 'fas fa-plus',
                shortcut: 'Alt+N',
                available: state.activeTab === 'task-board' && state.currentProjectId
            },
            {
                key: 'toggleSelection',
                title: '切換選擇模式',
                description: '進入或退出批量操作模式',
                icon: 'fas fa-check-square',
                shortcut: 'Alt+S',
                available: state.activeTab === 'task-board' && state.currentProjectId
            },
            {
                key: 'selectAll',
                title: '全選任務',
                description: '選擇當前專案的所有任務',
                icon: 'fas fa-check-double',
                shortcut: 'Alt+A',
                available: state.ui.bulkOperationMode
            },
            {
                key: 'bulkComplete',
                title: '批量完成',
                description: '將選中的任務標記為完成',
                icon: 'fas fa-check-circle',
                shortcut: 'Alt+Shift+C',
                available: state.ui.bulkOperationMode && state.ui.selectedTasks.size > 0
            },
            {
                key: 'bulkMove',
                title: '批量移動',
                description: '將選中的任務移動到其他專案',
                icon: 'fas fa-arrows-alt',
                shortcut: 'Alt+Shift+M',
                available: state.ui.bulkOperationMode && state.ui.selectedTasks.size > 0
            },
            {
                key: 'bulkDelete',
                title: '批量刪除',
                description: '刪除選中的任務',
                icon: 'fas fa-trash',
                shortcut: 'Alt+Shift+D',
                available: state.ui.bulkOperationMode && state.ui.selectedTasks.size > 0
            },
            {
                key: 'projectBoard',
                title: '專案看板',
                description: '切換到專案看板頁面',
                icon: 'fas fa-th-large',
                shortcut: 'Alt+1',
                available: state.activeTab !== 'project-board'
            },
            {
                key: 'taskBoard',
                title: '任務看板',
                description: '切換到任務看板頁面',
                icon: 'fas fa-tasks',
                shortcut: 'Alt+2',
                available: state.activeTab !== 'task-board'
            },
            {
                key: 'statistics',
                title: '統計分析',
                description: '查看專案和任務統計',
                icon: 'fas fa-chart-bar',
                shortcut: 'Alt+3',
                available: state.activeTab !== 'statistics'
            },
            {
                key: 'settings',
                title: '設定',
                description: '應用程式設定和配置',
                icon: 'fas fa-cog',
                shortcut: 'Alt+4',
                available: state.activeTab !== 'settings'
            },
            {
                key: 'export',
                title: '匯出資料',
                description: '匯出所有專案和任務資料',
                icon: 'fas fa-download',
                shortcut: 'Alt+E',
                available: true
            },
            {
                key: 'help',
                title: '快捷鍵幫助',
                description: '查看所有可用的快捷鍵',
                icon: 'fas fa-question-circle',
                shortcut: 'F1',
                available: true
            }
        ];

        return actions.filter(action => action.available);
    }

    // 綁定快速操作事件
    bindQuickActionEvents(modal) {
        let selectedIndex = 0;
        const items = modal.element.querySelectorAll('.quick-action-item');

        const updateSelection = () => {
            items.forEach((item, index) => {
                item.classList.toggle('bg-blue-50', index === selectedIndex);
            });
        };

        const executeAction = () => {
            const selectedItem = items[selectedIndex];
            if (selectedItem) {
                const action = selectedItem.dataset.action;
                this.executeQuickAction(action);
                modal.close();
            }
        };

        // 初始選擇
        updateSelection();

        // 鍵盤事件
        const keyHandler = (e) => {
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                    updateSelection();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, 0);
                    updateSelection();
                    break;
                case 'Enter':
                    e.preventDefault();
                    executeAction();
                    break;
            }
        };

        // 滑鼠事件
        items.forEach((item, index) => {
            item.addEventListener('mouseenter', () => {
                selectedIndex = index;
                updateSelection();
            });

            item.addEventListener('click', () => {
                selectedIndex = index;
                executeAction();
            });
        });

        document.addEventListener('keydown', keyHandler);

        // 清理事件
        modal.element.addEventListener('remove', () => {
            document.removeEventListener('keydown', keyHandler);
        });
    }

    // 執行快速操作
    executeQuickAction(action) {
        switch (action) {
            case 'search':
                const searchInput = document.getElementById('global-search');
                if (searchInput) {
                    searchInput.focus();
                    searchInput.select();
                }
                break;
            case 'newTask':
                const taskInput = document.getElementById('new-task-input');
                if (taskInput) {
                    taskInput.focus();
                }
                break;
            case 'toggleSelection':
                document.dispatchEvent(new CustomEvent('toggleSelectionMode'));
                break;
            case 'selectAll':
                document.dispatchEvent(new CustomEvent('selectAllTasks'));
                break;
            case 'bulkComplete':
                document.dispatchEvent(new CustomEvent('bulkCompleteTasks'));
                break;
            case 'bulkMove':
                document.dispatchEvent(new CustomEvent('bulkMoveTasks'));
                break;
            case 'bulkDelete':
                document.dispatchEvent(new CustomEvent('bulkDeleteTasks'));
                break;
            case 'projectBoard':
                this.switchTab('project-board');
                break;
            case 'taskBoard':
                this.switchTab('task-board');
                break;
            case 'statistics':
                this.switchTab('statistics');
                break;
            case 'settings':
                this.switchTab('settings');
                break;
            case 'export':
                document.dispatchEvent(new CustomEvent('exportData'));
                break;
            case 'help':
                this.showHelpModal();
                break;
        }
    }

    // 顯示幫助模態框
    showHelpModal() {
        const shortcuts = Array.from(this.shortcuts.entries())
            .map(([combo, data]) => ({
                combo: data.originalCombo,
                description: data.description
            }))
            .filter(item => item.description)
            .sort((a, b) => a.description.localeCompare(b.description));

        const shortcutList = shortcuts.map(item => `
            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                <span class="text-sm text-gray-700">${item.description}</span>
                <kbd class="px-2 py-1 text-xs font-mono bg-gray-100 border border-gray-300 rounded">${item.combo}</kbd>
            </div>
        `).join('');

        const content = `
            <div class="max-h-96 overflow-y-auto">
                <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                    <p class="text-blue-800 font-medium">💡 提示</p>
                    <p class="text-blue-700 text-sm">按 <kbd class="px-1 py-0.5 bg-blue-200 rounded text-xs">Alt+Q</kbd> 開啟快速操作清單，避免快捷鍵衝突</p>
                </div>
                <p class="text-gray-600 mb-4">以下是可用的鍵盤快捷鍵：</p>
                <div class="space-y-1">
                    ${shortcutList}
                </div>
            </div>
        `;

        new Modal({
            title: '鍵盤快捷鍵',
            content,
            size: 'lg',
            buttons: [
                {
                    id: 'close',
                    text: '關閉',
                    type: 'primary'
                }
            ]
        }).open();
    }

    // 啟用/禁用快捷鍵
    setEnabled(enabled) {
        this.isEnabled = enabled;
    }

    // 獲取所有快捷鍵
    getAllShortcuts() {
        return Array.from(this.shortcuts.entries()).map(([combo, data]) => ({
            combo: data.originalCombo,
            description: data.description
        }));
    }

    // 檢查快捷鍵是否存在
    hasShortcut(keyCombo) {
        const normalizedCombo = this.normalizeKeyCombo(keyCombo);
        return this.shortcuts.has(normalizedCombo);
    }

    // 更新快捷鍵
    updateShortcut(oldCombo, newCombo, callback, description) {
        this.unregister(oldCombo);
        this.register(newCombo, callback, description);
    }

    // 臨時禁用快捷鍵
    temporaryDisable(duration = 1000) {
        this.setEnabled(false);
        setTimeout(() => {
            this.setEnabled(true);
        }, duration);
    }

    // 獲取快捷鍵統計
    getShortcutStats() {
        return {
            totalShortcuts: this.shortcuts.size,
            enabled: this.isEnabled,
            activeModals: this.activeModals
        };
    }
}

// 導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ShortcutManager;
} else {
    window.ShortcutManager = ShortcutManager;
}
