# 專案代辦事項管理 - V3 功能規劃

## 1. 專案願景 (Project Vision)

打造一個採用現代化介面 (Modern UI) 的智慧專案管理工具。透過引入 Tailwind CSS，我們將建構一個乾淨、響應式且以卡片為基礎的佈局。功能上，透過分頁 (Tabs) 將工作區與設定區明確分離，提升使用者專注度。AI 助理和完整的資料匯入/匯出功能，共同構成一個強大且使用者友好的個人生產力平台。

---

## 2. UI/UX 核心概念

- **佈局**：應用程式將採用分頁式主介面。
    - **Tab 1: 專案看板 (Board)**：這是使用者的主要工作區，包含專案選擇、進度顯示和任務卡片列表。
    - **Tab 2: 設定 (Settings)**：此頁面專門用於配置應用程式，包含 Gemini API 金鑰設定和資料匯入/匯出功能。
- **樣式**：全面採用 **Tailwind CSS** 進行樣式設計，確保視覺一致性、可維護性和現代感。
- **任務呈現**：每個主任務都將是一個獨立的 **卡片 (Card)**。卡片內部會清晰地劃分出標題、進度、日期和子任務區域。

---

## 3. 使用者故事 (User Stories)

### 故事一：工作區管理
**作為一個使用者，我想要：**
- 在「專案看板」分頁中，透過一個下拉式選單來快速切換不同的專案。
- 在看板頂部看到當前專案的總體完成度進度條。
- 以卡片的形式查看所有待辦和已完成的任務，每個卡片都像一個小的工作單元。
- 在任務卡片上，能清楚地看到子任務的完成統計、創建日期和完成日期。

### 故事二：應用程式設定
**作為一個使用者，我想要：**
- 點擊「設定」分頁，進入一個專門的頁面來管理我的應用程式。
- 在設定頁中，找到輸入我的 Gemini API 金鑰的區塊，並有清晰的說明指引我如何獲取它。
- 在同一個設定頁面，輕鬆地找到「匯出」和「匯入」按鈕，以備份或還原我的所有資料。

### 故事三：AI 智慧互動
**作為一個使用者，我想要：**
- 一個固定在畫面側邊的 AI 聊天視窗，隨時待命。
- 當我未設定 API 金鑰時，聊天功能應被鎖定，並友善地提示我前往「設定」分頁進行配置。
- **智慧專案規劃**：能夠給 AI 一個主題（例如「幫我規劃一個為期三個月的健身計畫」），AI 就能自動為我創建一個全新的專案，並在其中預先填滿結構化的、合乎邏輯的主任務（如「第一週：建立基礎」）和子任務（如「週一：有氧運動30分鐘」）。
- **快速任務創建**：同時，我也希望能繼續使用簡單的指令（例如「在目前專案新增任務：買乳清蛋白」）來快速新增單一任務。

---

## 4. 技術與資料規格

- **前端樣式**：Tailwind CSS (透過 CDN 引入)。
- **前端邏輯**：純 JavaScript (ES6+)。
- **核心 API**：Google Gemini API。
- **資料儲存**：瀏覽器 `localStorage`。
- **資料結構**：保持 V2 的結構，一個包含 `apiKey`, `currentProjectId`, 和 `projects` 陣列的根物件。

```json
{
  "apiKey": "...",
  "currentProjectId": "...",
  "projects": [ ... ]
}
```
