# 專案代辦事項管理 - V2 開發步驟

## 總覽

本文件是一個循序漸進的開發指南，旨在將 `功能規劃_V2.md` 中的使用者故事轉化為一個功能齊全的前端應用程式。開發將分為四個主要階段：**結構 -> 樣式 -> 核心邏輯 -> 整合**。

---

## 階段一：HTML 骨架建設 (The Skeleton)

**目標**：建立應用程式的所有靜態 UI 元素，並為它們分配清晰的 `id` 和 `class`，為後續的 CSS 和 JS 操作打下基礎。

1.  **檔案結構**：
    - 建立 `index.html`, `style.css`, `app.js`。

2.  **`index.html` 內容**：
    - **主佈局**：使用 `<body>` 作為 Flex 容器，劃分出左側的 AI 助理區 (`<aside id="ai-chat-widget">`) 和右側的主內容區 (`<div id="main-container">`)。
    - **AI 助理 (`#ai-chat-widget`)**：
        - 包含 API 金鑰設定區 (`#api-key-section`)，內有說明、輸入框和儲存按鈕。
        - 包含聊天歷史記錄區 (`#chat-history`) 和聊天輸入表單 (`#chat-form`)。
    - **專案抽屜 (`#project-drawer`)**：
        - 這是一個獨立的 `<aside>` 元素，預設在畫面外，透過 CSS class 控制其顯示。
        - 包含專案列表 (`#project-list`) 和新增專案的表單。
    - **主內容區 (`#main-container`)**：
        - **標頭 (`<header>`)**：包含開啟抽屜的漢堡按鈕、專案標題 (`#current-project-title`)，以及匯入/匯出按鈕。
        - **主體 (`<main>`)**：
            - 專案進度條容器 (`#project-progress-container`)。
            - 新增主任務的表單 (`#add-task-form`)。
            - 任務包裹容器 (`#tasks-wrapper`)，內含「待辦」和「已完成」兩個列表 (`#todo-tasks-list`, `#completed-tasks-list`)。

---

## 階段二：CSS 視覺美化 (The Look)

**目標**：將靜態的 HTML 骨架轉化為一個美觀、響應式且互動友好的介面。

1.  **佈局**：
    - 使用 Flexbox 實現 `#ai-chat-widget` 和 `#main-container` 的左右佈局。
    - 讓 `#main-container` 內部可以垂直滾動。
2.  **AI 助理樣式**：
    - 將其固定在左側，並設定固定寬度。
    - 建立 `.is-locked` class，用於在 API 金鑰未設定時，降低其不透明度並禁用互動。
3.  **專案抽屜樣式**：
    - 使用 `position: fixed` 和 `transform: translateX(-100%)` 將其隱藏。
    - 建立 `.is-open` class，使其平滑地滑入畫面。
4.  **任務樣式**：
    - 為任務項目設計卡片式外觀。
    - 為已完成的任務套用刪除線和半透明效果。
    - 使用 `padding-left` 和 `border-left` 來視覺化子任務的層級關係。

---

## 階段三：JavaScript 核心邏輯 (The Brain)

**目標**：實現所有不依賴外部 API 的核心功能，並建立一個穩定的狀態管理系統。

1.  **狀態管理 (`State Management`)**：
    - 建立一個全域 `state` 物件，其結構與 `功能規劃_V2.md` 中定義的藍圖一致。
    - 建立 `saveState()` 和 `loadState()` 函式，用於和 `localStorage` 進行同步。

2.  **渲染引擎 (`Render Engine`)**：
    - 建立一個主 `render()` 函式，作為所有 UI 更新的入口點。
    - `render()` 內部會呼叫 `renderProjects()` 和 `renderTasks()`。
    - `renderTasks()` 是最核心的函式，它會：
        - 根據 `state.currentProjectId` 找到當前專案。
        - 呼叫計算函式來更新專案進度條。
        - 遍歷專案中的任務，動態建立 DOM 元素，並根據 `task.completed` 狀態將其放入「待辦」或「已完成」列表。
        - 在建立主任務元素時，遞迴地建立其子任務列表。

3.  **核心功能 (`Core Functions`)**：
    - 建立 `addProject()`, `addTask()`, `addSubtask()` 等函式。這些函式只負責修改 `state` 物件，**不直接操作 DOM**。
    - 建立 `toggleTaskCompletion()`, `toggleSubtaskCompletion()`, `toggleTaskExpansion()` 等函式來更新狀態。

4.  **事件監聽 (`Event Listeners`)**：
    - 建立一個 `addEventListeners()` 函式來集中管理所有事件。
    - 使用**事件委派 (Event Delegation)** 模式來處理任務列表中的點擊（例如，點擊核取方塊、任務標題）。
    - 當事件觸發時，呼叫對應的核心功能函式來修改 `state`，然後**呼叫 `render()`** 來讓 UI 自動更新。

---

## 階段四：外部服務整合 (Integration)

**目標**：將核心應用與外部服務（檔案系統、Gemini API）連接起來。

1.  **資料匯入/匯出**：
    - **匯出**：為匯出按鈕新增事件監聽。讀取 `state` 物件，`JSON.stringify` 後，建立一個 `Blob` 並觸發下載。
    - **匯入**：為檔案輸入框監聽 `change` 事件。使用 `FileReader` 讀取檔案內容，`JSON.parse` 後，用其覆蓋 `state`，儲存並重新載入頁面。

2.  **AI 助理整合**：
    - **API 金鑰管理**：實作儲存 API 金鑰到 `state.apiKey` 的邏輯。
    - **UI 鎖定**：在主 `render()` 函式中，根據 `state.apiKey` 是否存在來切換 `#ai-chat-widget` 的 `.is-locked` class。
    - **API 呼叫 (模擬)**：
        - 建立一個 `handleAICommand(prompt)` 函式。
        - **(模擬階段)**：此函式暫時使用簡單的字串匹配來解析使用者輸入，並轉換成一個動作物件（例如 `{ type: 'addTask', payload: { text: '...' } }`）。
        - **(真實階段)**：未來可將此函式替換為對 Gemini API 的 `fetch` 請求，並解析其回傳的 JSON。
    - **動作執行**：建立 `executeAIAction(action)` 函式，它接收動作物件，並呼叫階段三中建立的核心功能函式（如 `addTask()`）來完成操作。
