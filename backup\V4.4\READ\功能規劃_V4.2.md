# 專案代辦事項管理 - V4.2 功能規劃

## 1. 專案願景

在 V4 的基礎上，V4.2 旨在進化為一個多維度的專案控制中心。透過引入全新的「專案看板」，使用者將獲得前所未有的專案鳥瞰能力。同時，透過對「任務看板」的精細化改造，使用者將能更深入地管理和聚焦於單一任務的細節，實現宏觀與微觀的無縫切換。

---

## 2. UI/UX 核心概念

- **分頁佈局 (Tabs)**：主介面擴展為三個核心分頁：
    - **Tab 1: 專案看板 (Project Board)** (新增): 提供所有專案的概覽，以卡片形式展示。
    - **Tab 2: 任務看板 (Task Board)** (原專案看板): 深入單一專案，展示其詳細的任務卡片列表。
    - **Tab 3: 設定 (Settings)**: 應用程式的全域設定。
- **卡片樣式 (Cards)**：
    - **專案卡片**: 在「專案看板」上，每個專案都是一張卡片，包含進度條和操作按鈕。
    - **任務卡片**: 在「任務看板」上，每個主任務都是一張卡片。卡片將擁有**最大寬度限制**，避免在寬螢幕上過度拉伸，使其保持優雅的閱讀寬度。

---

## 3. 核心功能詳解

### 3.1 專案看板 (Project Board)
- **專案展示**: 以卡片網格佈局顯示所有專案。
- **狀態顯示**: 每個專案卡片需根據其任務完成情況，自動計算並顯示其狀態（例如「執行中」或「已完成」）。
- **進度視覺化**: 每個專案卡片都包含一個進度條，直觀反映其完成度。
- **狀態篩選**: 提供「全部」、「執行中」、「已完成」的篩選按鈕，方便使用者聚焦。
- **快速導航**: 每個專案卡片上都有一個「查看專案」按鈕，點擊後會自動切換到「任務看板」分頁，並載入該專案的詳細任務。

### 3.2 任務看板 (Task Board)
- **子任務列表**: 每個任務卡片內部，子任務列表將被明確劃分為「待辦」和「已完成」兩個區塊。
- **子任務篩選**: 每個任務卡片上都將提供一組篩選器（例如「全部」、「待辦」、「已完成」），允許使用者獨立控制該卡片內顯示哪些狀態的子任務。
- **子任務屬性**: **每個子任務**現在都將擁有自己的 `creationDate` 和 `completionDate`，並顯示在子任務項目上。

### 3.3 AI 助理 & 設定
- (功能與 V4 保持一致) AI 助理負責智慧規劃和快速新增，設定頁面負責 API 金鑰和資料匯入/匯出。

---

## 4. 資料結構藍圖 (Data Schema)

```json
{
  "apiKey": "...",
  "currentProjectId": "...",
  "activeTab": "project-board", // 預設分頁變更
  "projectBoardFilter": "all", // 新增：專案看板的篩選狀態
  "projects": [
    {
      "id": "...",
      "name": "...",
      "tasks": [
        {
          "id": "...",
          "text": "...",
          "completed": false,
          "expanded": true,
          "subtaskFilter": "all", // 新增：每個任務自己的子任務篩選器
          "creationDate": "...",
          "completionDate": null,
          "subtasks": [
            {
              "id": "...",
              "text": "...",
              "completed": false,
              "creationDate": "...", // 新增
              "completionDate": null // 新增
            }
          ]
        }
      ]
    }
  ]
}
```
