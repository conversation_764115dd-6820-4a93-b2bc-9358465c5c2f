/**
 * Modal Component - V4.3
 * 模態對話框組件，支援多種類型和自訂內容
 */

class Modal {
    static activeModal = null;
    static backdrop = null;

    constructor(options = {}) {
        this.options = {
            title: options.title || '',
            content: options.content || '',
            size: options.size || 'md', // sm, md, lg, xl
            closable: options.closable !== false,
            closeOnBackdrop: options.closeOnBackdrop !== false,
            closeOnEscape: options.closeOnEscape !== false,
            showHeader: options.showHeader !== false,
            showFooter: options.showFooter !== false,
            buttons: options.buttons || [],
            onOpen: options.onOpen || (() => {}),
            onClose: options.onClose || (() => {}),
            className: options.className || '',
            ...options
        };

        this.element = null;
        this.isOpen = false;
    }

    open() {
        if (Modal.activeModal) {
            Modal.activeModal.close();
        }

        this.createModal();
        this.bindEvents();
        this.show();
        
        Modal.activeModal = this;
        this.isOpen = true;
        this.options.onOpen();

        return this;
    }

    close() {
        if (!this.isOpen) return;

        this.hide();
        
        setTimeout(() => {
            this.destroy();
            Modal.activeModal = null;
            this.isOpen = false;
            this.options.onClose();
        }, 300);

        return this;
    }

    createModal() {
        // 創建背景遮罩
        if (!Modal.backdrop) {
            Modal.backdrop = document.createElement('div');
            Modal.backdrop.className = 'modal-backdrop fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300 opacity-0';
            document.body.appendChild(Modal.backdrop);
        }

        // 創建模態框
        this.element = document.createElement('div');
        this.element.className = 'modal fixed inset-0 z-50 overflow-y-auto flex items-center justify-center p-4';
        this.element.innerHTML = this.getModalHTML();

        document.body.appendChild(this.element);
    }

    getModalHTML() {
        const sizeClasses = {
            sm: 'max-w-sm',
            md: 'max-w-md',
            lg: 'max-w-lg',
            xl: 'max-w-xl',
            '2xl': 'max-w-2xl',
            full: 'max-w-full mx-4'
        };

        const modalClass = [
            'modal-content',
            'bg-white',
            'rounded-lg',
            'shadow-xl',
            'transform',
            'transition-all',
            'duration-300',
            'scale-95',
            'opacity-0',
            'w-full',
            sizeClasses[this.options.size],
            this.options.className
        ].join(' ');

        return `
            <div class="${modalClass}">
                ${this.options.showHeader ? this.getHeaderHTML() : ''}
                <div class="modal-body p-6">
                    ${this.options.content}
                </div>
                ${this.options.showFooter ? this.getFooterHTML() : ''}
            </div>
        `;
    }

    getHeaderHTML() {
        return `
            <div class="modal-header flex items-center justify-between p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    ${this.options.title}
                </h3>
                ${this.options.closable ? `
                    <button class="modal-close text-gray-400 hover:text-gray-600 text-xl leading-none" data-action="close">
                        &times;
                    </button>
                ` : ''}
            </div>
        `;
    }

    getFooterHTML() {
        if (this.options.buttons.length === 0) {
            return '';
        }

        const buttonsHTML = this.options.buttons.map(button => {
            const btnClass = this.getButtonClass(button.type || 'secondary');
            return `
                <button class="${btnClass}" data-action="${button.action || 'custom'}" data-button-id="${button.id || ''}">
                    ${button.icon ? `<i class="${button.icon} mr-2"></i>` : ''}
                    ${button.text}
                </button>
            `;
        }).join('');

        return `
            <div class="modal-footer flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
                ${buttonsHTML}
            </div>
        `;
    }

    getButtonClass(type) {
        const baseClass = 'px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
        
        const typeClasses = {
            primary: 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500',
            secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
            danger: 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500',
            success: 'bg-green-500 text-white hover:bg-green-600 focus:ring-green-500'
        };

        return `${baseClass} ${typeClasses[type] || typeClasses.secondary}`;
    }

    bindEvents() {
        // 點擊事件
        this.element.addEventListener('click', (e) => {
            const action = e.target.dataset.action;

            if (action === 'close') {
                e.preventDefault();
                e.stopPropagation();
                this.close();
            } else if (action === 'custom') {
                e.preventDefault();
                e.stopPropagation();
                const buttonId = e.target.dataset.buttonId;
                const button = this.options.buttons.find(b => b.id === buttonId);
                if (button && button.onClick) {
                    const result = button.onClick(this);
                    if (result !== false && button.closeOnClick !== false) {
                        this.close();
                    }
                }
            }
        });

        // 背景點擊關閉
        if (this.options.closeOnBackdrop) {
            this.element.addEventListener('click', (e) => {
                // 只有點擊背景（modal容器本身）時才關閉
                if (e.target === this.element) {
                    this.close();
                }
            });
        }

        // ESC鍵關閉
        if (this.options.closeOnEscape) {
            this.escapeHandler = (e) => {
                if (e.key === 'Escape') {
                    this.close();
                }
            };
            document.addEventListener('keydown', this.escapeHandler);
        }
    }

    show() {
        // 顯示背景遮罩
        requestAnimationFrame(() => {
            Modal.backdrop.classList.remove('opacity-0');
            Modal.backdrop.classList.add('opacity-100');
        });

        // 顯示模態框
        requestAnimationFrame(() => {
            const content = this.element.querySelector('.modal-content');
            content.classList.remove('scale-95', 'opacity-0');
            content.classList.add('scale-100', 'opacity-100');
        });

        // 防止背景滾動
        document.body.style.overflow = 'hidden';
    }

    hide() {
        // 隱藏模態框
        const content = this.element.querySelector('.modal-content');
        content.classList.remove('scale-100', 'opacity-100');
        content.classList.add('scale-95', 'opacity-0');

        // 隱藏背景遮罩
        Modal.backdrop.classList.remove('opacity-100');
        Modal.backdrop.classList.add('opacity-0');

        // 恢復背景滾動
        document.body.style.overflow = '';
    }

    destroy() {
        if (this.element) {
            this.element.remove();
            this.element = null;
        }

        if (Modal.backdrop) {
            Modal.backdrop.remove();
            Modal.backdrop = null;
        }

        if (this.escapeHandler) {
            document.removeEventListener('keydown', this.escapeHandler);
        }
    }

    // 更新內容
    updateContent(content) {
        if (this.element) {
            const body = this.element.querySelector('.modal-body');
            if (body) {
                body.innerHTML = content;
            }
        }
    }

    // 靜態方法：快速創建常用模態框
    static alert(title, message, options = {}) {
        return new Modal({
            title,
            content: `<p class="text-gray-700">${message}</p>`,
            buttons: [
                {
                    id: 'ok',
                    text: '確定',
                    type: 'primary',
                    onClick: () => true
                }
            ],
            ...options
        }).open();
    }

    static confirm(title, message, onConfirm, options = {}) {
        return new Modal({
            title,
            content: `<p class="text-gray-700">${message}</p>`,
            buttons: [
                {
                    id: 'cancel',
                    text: '取消',
                    type: 'secondary',
                    onClick: () => true
                },
                {
                    id: 'confirm',
                    text: '確定',
                    type: 'primary',
                    onClick: () => {
                        onConfirm();
                        return true;
                    }
                }
            ],
            ...options
        }).open();
    }

    static prompt(title, message, defaultValue = '', onSubmit, options = {}) {
        const inputId = 'modal-prompt-input';
        const content = `
            <p class="text-gray-700 mb-4">${message}</p>
            <input type="text" id="${inputId}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" value="${defaultValue}" placeholder="請輸入...">
        `;

        return new Modal({
            title,
            content,
            buttons: [
                {
                    id: 'cancel',
                    text: '取消',
                    type: 'secondary',
                    onClick: () => true
                },
                {
                    id: 'submit',
                    text: '確定',
                    type: 'primary',
                    onClick: (modal) => {
                        const input = modal.element.querySelector(`#${inputId}`);
                        const value = input.value.trim();
                        if (value) {
                            onSubmit(value);
                            return true;
                        }
                        return false;
                    }
                }
            ],
            onOpen: () => {
                setTimeout(() => {
                    const input = document.getElementById(inputId);
                    if (input) {
                        input.focus();
                        input.select();
                    }
                }, 100);
            },
            ...options
        }).open();
    }
}

// 導出組件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Modal;
}
