# 專案代辦事項管理網站 - 開發步驟

本文件提供一個從零開始建立專案代辦事項管理網站的詳細步驟，涵蓋了從環境設定到功能實現的完整流程。

---

## 階段一：環境與檔案結構設定

1.  **建立專案資料夾**：
    - 在您選擇的位置建立一個新的資料夾，例如 `project-todo-app`。

2.  **建立初始檔案**：
    - 在專案資料夾中，建立以下三個空白檔案：
      - `index.html` (HTML 結構)
      - `style.css` (CSS 樣式)
      - `app.js` (JavaScript 邏輯)

---

## 階段二：HTML 結構 (index.html)

這是網站的骨架。目標是建立所有必要的 UI 元素，並給予它們唯一的 ID 或 class 以便後續用 CSS 和 JavaScript 操作。

1.  **基礎 HTML 模板**：
    - 建立一個標準的 HTML5 文件結構，引入 `style.css` 和 `app.js`。
    ```html
    <!DOCTYPE html>
    <html lang="zh-Hant">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>專案代辦事項管理</title>
        <link rel="stylesheet" href="style.css">
    </head>
    <body>
        <!-- 內容將會加在這裡 -->
        <script src="app.js"></script>
    </body>
    </html>
    ```

2.  **AI 聊天視窗結構**：
    - 在 `<body>` 的頂層，新增一個 AI 聊天視窗的容器 (`<aside id="ai-chat-widget">`)。
    - 該容器應包含：
        - 一個顯示對話歷史的區域 (`<div id="chat-history">`)。
        - 一個包含輸入框和送出按鈕的表單 (`<form id="chat-form">`)。
        - 一個用於輸入和儲存 Gemini API 金鑰的設定區塊。

3.  **主要介面佈局**：
    - **標頭 (Header)**：包含網站標題和一個用於開啟抽屜的「選單」按鈕。
    - **專案抽屜 (Drawer)**：預設隱藏，包含專案列表 (`<ul>`) 和新增專案的表單。
    - **主內容區 (Main Content)**：顯示當前專案名稱、新增任務的表單，以及任務列表的容器。
    - **匯出按鈕**：一個用於觸發 JSON 匯出的按鈕。
    - **匯入按鈕**：一個 `<label>` 包裹的 `<input type="file">`，樣式化成按鈕，用於觸發檔案選擇和匯入。

3.  **進度條與統計顯示**：
    - 在主內容區的專案標題下方，新增一個進度條元素 (`<progress>`) 和一個文字標籤，用於顯示專案的總體完成百分比。
    - 在任務列表的 `<li>` 模板中，也為主任務預留一個位置來顯示其子任務的完成統計 (例如 `<span>(2/5)</span>`)。

4.  **任務列表結構**：
    - 在主內容區，預先定義好「待辦」和「已完成」兩個區塊的容器，以便 JavaScript 渲染時使用。
    ```html
    <main>
        <h2 id="current-project-title"></h2>
        <!-- 新增主任務的表單 -->

        <div id="tasks-container">
            <h3>待辦任務</h3>
            <ul id="todo-tasks-list"></ul>
            
            <hr>

            <h3>已完成任務</h3>
            <ul id="completed-tasks-list"></ul>
        </div>
    </main>
    ```

---

## 階段三：CSS 樣式 (style.css)

此階段專注於美化介面，使其直觀且易於使用。

1.  **全域樣式**：設定 `box-sizing: border-box;`，並定義基本的字體、顏色和背景。
2.  **AI 聊天視窗樣式**：
    - 使用 `position: fixed` 將聊天視窗固定在頁面左下角或左側。
    - 設計對話泡泡的樣式，區分使用者訊息和 AI 回覆。
    - **鎖定狀態樣式**：建立一個例如 `.chat-widget.is-locked` 的 class，它會將整個聊天視窗的透明度降低，並禁用輸入框 (`pointer-events: none;`)。
    - 確保聊天視窗在不同螢幕尺寸下都能正常顯示。

3.  **抽屜 (Drawer) 樣式**：
    - 使用 `position: fixed`，並透過 `transform: translateX(-100%);` 將其隱藏在畫面左側。
    - 建立一個 `.drawer.is-open` class，將其 `transform` 設為 `translateX(0)`。
    - 使用 `transition` 屬性來產生平滑的滑入滑出動畫。
3.  **任務項目 (Task Item) 樣式**：
    - 設計清晰的列表佈局，使用 Flexbox 來對齊項目（核取方塊、文字、按鈕）。
    - **子任務縮排**：使用 `margin-left` 或 `padding-left` 來視覺上區分層級。
    - **已完成樣式**：為已完成的任務（`li.completed`）套用刪除線、灰色文字或較低的透明度。
    - **日期樣式**：將創建和完成日期的文字設為較小、較淺的顏色。
4.  **互動效果**：為所有按鈕、可點擊的連結和任務項目新增 `:hover` 效果。

---

## 階段四：JavaScript 邏輯 (app.js)

這是專案的核心，所有動態功能都在此實現。

1.  **DOM 元素選擇**：
    - 在指令碼的開頭，使用 `document.getElementById` 或 `document.querySelector` 獲取所有需要操作的 HTML 元素，並將它們儲存在常數中。

2.  **資料管理**：
    - **狀態變數**：建立一個全域變數（例如 `let state`）來儲存整個應用程式的狀態，包含 `apiKey` 和 `projects` 陣列。
    - **初始化 (`init`)**：
        - 撰寫一個 `init` 函式，在頁面載入時執行。
        - 此函式會檢查 `localStorage` 中是否有已儲存的狀態物件，若有則載入，否則建立一個包含 `apiKey: null` 和 `projects: []` 的預設狀態。
        - `init` 函式最後會呼叫渲染函式來顯示初始畫面。
    - **儲存資料**：建立一個 `saveState()` 函式，每次狀態變更時，就將 `state` 物件轉換為 JSON 字串並存入 `localStorage`。

3.  **計算函式 (`calculate`)**：
    - **`calculateTaskProgress(task)`**：建立一個輔助函式，接收一個主任務物件，計算其子任務的完成數量和總數，並返回一個物件，例如 `{ completed: 3, total: 5 }`。
    - **`calculateProjectProgress(project)`**：建立一個函式，接收一個專案物件，遍歷其下所有任務和子任務，計算總體的完成百分比。

4.  **渲染函式 (`render`)**：
    - **`render()` 主函式**：這是一個高階函式，它會呼叫所有其他的渲染函式。它還會檢查 `state.apiKey` 是否存在，並據此為 AI 聊天視窗新增或移除 `.is-locked` class。
    - **`renderProjects()`**：清空抽屜中的專案列表，並根據 `state.projects` 重新產生所有專案項目。
    - **`renderTasks()`**：
        - 這是最複雜的渲染函式。
        - 在渲染之前，呼叫 `calculateProjectProgress` 來更新專案進度條的值和文字。
        - 在渲染每個主任務時，呼叫 `calculateTaskProgress` 並將結果顯示在任務項目上。
        - 清空「待辦任務」和「已完成任務」兩個 `<ul>` 容器。
        - 遍歷當前專案的 `tasks` 陣列。
        - 對於每個主任務，根據其 `completed` 狀態，建立對應的 `<li>` 元素並附加到正確的列表中。
        - 在建立主任務 `<li>` 的過程中，如果它有 `subtasks`，則在其內部遞迴地建立「待辦子任務」和「已完成子任務」的列表。
        - 確保所有元素都綁定了正確的 `data-id` 屬性，以便事件處理。

4.  **事件監聽器 (`addEventListeners`)**：
    - 建立一個函式來集中管理所有的事件監聽。
    - **新增專案/任務**：監聽各個「新增」按鈕的點擊事件。從輸入框獲取值，更新 `state` 物件，儲存資料，然後重新渲染。
    - **切換專案**：使用事件委派 (event delegation) 監聽抽屜專案列表的點擊。獲取被點擊專案的 ID，更新 `state.currentProjectId`，然後重新渲染任務視圖。
    - **切換完成狀態**：監聽任務容器的點擊事件。檢查被點擊的是否為核取方塊 (`<input type="checkbox">`)。若是，則找到對應的任務 ID，更新其 `completed` 狀態和 `completionDate`，儲存並重新渲染。
    - **展開/收折任務**：監聽任務文字的點擊。切換對應任務的 `expanded` 屬性，然後重新渲染。
    - **匯出資料**：監聽匯出按鈕的點擊。將完整的 `state` 物件轉換為格式化的 JSON，並觸發檔案下載。
    - **匯入資料**：監聽檔案輸入框的 `change` 事件。使用 `FileReader` API 來讀取使用者選擇的檔案。解析 JSON，進行簡單的結構驗證（例如，檢查 `apiKey` 和 `projects` 屬性是否存在），然後用讀取到的內容覆蓋 `state`，儲存到 `localStorage`，最後呼叫 `location.reload()` 來重新載入應用程式。

5.  **AI 助理邏輯**：
    - **API 金鑰處理**：
        - 建立函式來更新 `state.apiKey` 的值。
        - 在 AI 聊天視窗提供輸入欄位和「儲存」按鈕讓使用者設定金鑰。儲存後，更新 `state`，儲存並重新渲染。
        - 在設定區塊提供如何取得 API 金鑰的文字說明與連結。
    - **與 Gemini API 互動**：
        - 建立一個 `callGeminiAPI(prompt)` 函式，負責向 Gemini API 發送請求。
        - 這個函式會將使用者的輸入包裝成一個特定的 `prompt`，這個 `prompt` 會引導 AI 回應一個結構化的指令（例如 JSON 格式），而不是閒聊。
        - **Prompt Engineering (提示工程)** 是關鍵。範例提示可能如下：
          ```
          你是一個任務管理助理。根據使用者的要求，回傳一個 JSON 物件。JSON 必須包含 'action' (例如 'addProject', 'addTask', 'addSubtask') 和 'payload' (包含任務名稱、父任務名稱等)。
          使用者說："幫我新增一個叫『市場調研』的任務"
          你應該回傳：{"action": "addTask", "payload": {"text": "市場調研"}}
          ```
    - **解析 AI 回應**：
        - 收到 Gemini API 的 JSON 回應後，撰寫一個 `executeAICommand(command)` 函式。
        - 這個函式會使用 `switch` 或 `if/else` 來判斷 `command.action` 的值，並呼叫對應的現有函式（例如 `addProject`, `addTask`）來修改應用程式的狀態。
    - **事件監聽**：
        - 監聽聊天視窗表單的提交事件。獲取使用者輸入，將其傳送給 `callGeminiAPI`，並在等待回應時顯示一個「載入中」的提示。
        - 將 AI 的回覆和使用者的訊息都顯示在聊天歷史記錄中。

6.  **啟動應用**：
    - 在 `app.js` 的最後，呼叫 `init()` 函式來啟動整個應用程式。
