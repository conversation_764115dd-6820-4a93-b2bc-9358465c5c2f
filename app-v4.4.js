/**
 * 專案代辦事項管理 V4.4 - 主應用程式
 * 整合所有V4.4新功能的主控制器
 * 新增功能：任務詳述顯示與編輯功能
 */

document.addEventListener('DOMContentLoaded', () => {
    
    // --- 全域管理器實例 ---
    let searchManager;
    let selectionManager;
    let bulkOperationManager;
    let statisticsManager;
    let shortcutManager;
    // let searchBox; // 暫時註解，未來可能使用

    // --- DOM 元素選擇 ---
    const dom = {
        // AI Chat
        aiChatWidget: document.getElementById('ai-chat-widget'),
        chatHistory: document.getElementById('chat-history'),
        chatForm: document.getElementById('chat-form'),
        chatInput: document.getElementById('chat-input'),

        // Tabs
        tabButtons: document.querySelectorAll('.tab-btn'),
        projectBoardTabContent: document.getElementById('project-board-tab-content'),
        taskBoardTabContent: document.getElementById('task-board-tab-content'),
        statisticsTabContent: document.getElementById('statistics-tab-content'),
        settingsTabContent: document.getElementById('settings-tab-content'),

        // Search and Quick Actions
        quickActionBtn: document.getElementById('quick-action-btn'),
        searchContainer: document.querySelector('.search-container'),
        globalSearch: document.getElementById('global-search'),
        searchResults: document.getElementById('search-results'),

        // Project Board
        projectFilterButtons: document.getElementById('project-filter-buttons'),
        projectCardGrid: document.getElementById('project-card-grid'),

        // Task Board
        bulkOperationsToolbar: document.getElementById('bulk-operations-toolbar'),
        projectSelector: document.getElementById('project-selector'),
        addProjectForm: document.getElementById('add-project-form'),
        newProjectInput: document.getElementById('new-project-input'),
        projectProgressContainer: document.getElementById('project-progress-container'),
        projectProgressBar: document.getElementById('project-progress-bar'),
        projectProgressText: document.getElementById('project-progress-text'),
        addTaskForm: document.getElementById('add-task-form'),
        newTaskInput: document.getElementById('new-task-input'),
        todoTasksList: document.getElementById('todo-tasks-list'),
        completedTasksList: document.getElementById('completed-tasks-list'),

        // Statistics
        totalTasksStat: document.getElementById('total-tasks-stat'),
        completedTasksStat: document.getElementById('completed-tasks-stat'),
        totalProjectsStat: document.getElementById('total-projects-stat'),
        avgCompletionTimeStat: document.getElementById('avg-completion-time-stat'),

        // Settings
        apiKeyInput: document.getElementById('api-key-input'),
        saveApiKeyBtn: document.getElementById('save-api-key-btn'),
        exportJsonBtn: document.getElementById('export-json-btn'),
        importJsonInput: document.getElementById('import-json-input')
    };

    // --- 初始化管理器 ---
    const initializeManagers = () => {
        // 搜尋管理器
        searchManager = new SearchManager(stateManager);
        
        // 選擇管理器
        selectionManager = new SelectionManager(stateManager);
        
        // 批量操作管理器
        bulkOperationManager = new BulkOperationManager(stateManager, selectionManager);
        
        // 統計管理器
        statisticsManager = new StatisticsManager(stateManager);
        
        // 快捷鍵管理器
        shortcutManager = new ShortcutManager(stateManager);
        
        // 搜尋框組件
        if (dom.searchContainer) {
            // searchBox = new SearchBox(dom.searchContainer, searchManager);
            new SearchBox(dom.searchContainer, searchManager);
        }

        // 綁定選擇管理器事件
        selectionManager.bindEvents();
    };

    // --- 渲染引擎 ---
    const render = () => {
        renderTabs();
        renderAIChat();
        
        const state = stateManager.getState();
        
        switch (state.activeTab) {
            case 'project-board':
                renderProjectBoard();
                break;
            case 'task-board':
                renderTaskBoard();
                break;
            case 'statistics':
                renderStatistics();
                break;
            case 'settings':
                renderSettings();
                break;
        }
    };

    const renderTabs = () => {
        const state = stateManager.getState();
        
        dom.projectBoardTabContent.classList.toggle('hidden', state.activeTab !== 'project-board');
        dom.taskBoardTabContent.classList.toggle('hidden', state.activeTab !== 'task-board');
        dom.statisticsTabContent.classList.toggle('hidden', state.activeTab !== 'statistics');
        dom.settingsTabContent.classList.toggle('hidden', state.activeTab !== 'settings');
        
        dom.tabButtons.forEach(btn => {
            const tab = btn.dataset.tab;
            btn.classList.toggle('text-blue-500', tab === state.activeTab);
            btn.classList.toggle('border-b-2', tab === state.activeTab);
            btn.classList.toggle('border-blue-500', tab === state.activeTab);
        });
    };

    const renderAIChat = () => {
        const state = stateManager.getState();
        const isLocked = !state.apiKey;

        dom.aiChatWidget.classList.toggle('opacity-60', isLocked);
        dom.chatInput.disabled = isLocked;
        dom.chatForm.querySelector('button').disabled = isLocked;

        // 初始化聊天歷史（如果為空）
        if (dom.chatHistory.children.length === 0) {
            initializeChatHistory(isLocked);
        } else {
            // 檢查是否需要更新歡迎訊息（API KEY狀態改變時）
            const firstMessage = dom.chatHistory.firstChild;
            if (firstMessage && firstMessage.querySelector('.fas')) {
                const hasKeyIcon = firstMessage.querySelector('.fa-key');
                const hasRobotIcon = firstMessage.querySelector('.fa-robot');

                // 如果當前狀態與顯示的訊息不符，重新初始化
                if ((isLocked && hasRobotIcon) || (!isLocked && hasKeyIcon)) {
                    dom.chatHistory.innerHTML = '';
                    initializeChatHistory(isLocked);
                }
            }
        }

        if (isLocked) {
            dom.chatInput.placeholder = '請先在「設定」中提供 API 金鑰';
        } else {
            dom.chatInput.placeholder = '跟 AI 說話...';
        }
    };

    // 初始化聊天歷史
    const initializeChatHistory = (isLocked) => {
        const welcomeMessage = document.createElement('div');
        welcomeMessage.className = 'flex items-start space-x-3 mb-4';

        if (isLocked) {
            // 沒有API KEY時的提示
            welcomeMessage.innerHTML = `
                <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-key text-white text-sm"></i>
                </div>
                <div class="flex-1 bg-orange-50 border border-orange-200 rounded-lg p-3">
                    <div class="text-sm text-orange-800">
                        <div class="font-semibold mb-2">🔑 需要設定 API 金鑰</div>
                        <div class="mb-2">要使用 AI 助理功能，請先設定您的 Gemini API 金鑰：</div>
                        <div class="space-y-1 text-xs">
                            <div>1. 點擊右上角的 <i class="fas fa-cog"></i> 設定按鈕</div>
                            <div>2. 前往 <a href="https://aistudio.google.com/app/apikey" target="_blank" class="text-blue-600 hover:underline">Google AI Studio</a> 取得免費 API 金鑰</div>
                            <div>3. 將 API 金鑰貼到設定頁面並儲存</div>
                        </div>
                        <div class="mt-2 text-xs text-orange-600">
                            💡 Gemini API 提供免費額度，足夠日常使用
                        </div>
                    </div>
                </div>
            `;
        } else {
            // 有API KEY時的歡迎訊息
            welcomeMessage.innerHTML = `
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-robot text-white text-sm"></i>
                </div>
                <div class="flex-1 bg-gray-100 rounded-lg p-3">
                    <div class="text-sm text-gray-700">
                        👋 您好！我是您的AI任務管理助理。<br>
                        您可以告訴我：<br>
                        • 「新增任務：買牛奶」<br>
                        • 「規劃一個週末旅遊行程」<br>
                        • 「幫我整理工作任務」<br><br>
                        我會幫您智慧地管理任務和專案！
                    </div>
                </div>
            `;
        }

        dom.chatHistory.appendChild(welcomeMessage);
    };

    const renderProjectBoard = () => {
        const state = stateManager.getState();
        
        dom.projectCardGrid.innerHTML = '';
        const filteredProjects = state.projects.filter(p => {
            if (state.projectBoardFilter === 'all') return true;
            const progress = calculateProjectProgress(p);
            if (state.projectBoardFilter === 'inprogress') return progress.percentage < 100;
            if (state.projectBoardFilter === 'completed') return progress.percentage === 100;
            return false;
        });

        if (filteredProjects.length === 0) {
            if (state.projects.length === 0) {
                dom.projectCardGrid.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <i class="fas fa-folder-open text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">還沒有任何專案</h3>
                        <p class="text-gray-500 mb-4">開始建立您的第一個專案來管理任務吧！</p>
                        <button onclick="document.querySelector('[data-tab=\\"task-board\\"]').click()" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600">
                            <i class="fas fa-plus mr-2"></i>建立專案
                        </button>
                    </div>
                `;
            } else {
                dom.projectCardGrid.innerHTML = '<p class="text-gray-500 col-span-full text-center py-8">沒有符合篩選條件的專案。</p>';
            }
        } else {
            filteredProjects.forEach(p => {
                const card = createProjectCard(p);
                dom.projectCardGrid.appendChild(card);
            });
        }

        // 更新篩選按鈕狀態
        dom.projectFilterButtons.querySelectorAll('button').forEach(btn => {
            btn.classList.toggle('bg-blue-500', btn.dataset.filter === state.projectBoardFilter);
            btn.classList.toggle('text-white', btn.dataset.filter === state.projectBoardFilter);
        });
    };

    const renderTaskBoard = () => {
        const state = stateManager.getState();
        
        renderProjectSelector();
        const project = state.projects.find(p => p.id === state.currentProjectId);
        
        if (!project) {
            dom.projectProgressContainer.classList.add('hidden');
            dom.addTaskForm.classList.add('hidden');
            dom.todoTasksList.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-arrow-left text-4xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500 mb-4">請從「專案看板」選擇一個專案，或在上方建立新專案。</p>
                    <button onclick="document.querySelector('[data-tab=\\"project-board\\"]').click()" class="text-blue-500 hover:text-blue-700 underline">
                        <i class="fas fa-th-large mr-1"></i>前往專案看板
                    </button>
                </div>
            `;
            dom.completedTasksList.innerHTML = '';
            return;
        }

        dom.projectProgressContainer.classList.remove('hidden');
        dom.addTaskForm.classList.remove('hidden');

        const projectProgress = calculateProjectProgress(project);
        dom.projectProgressBar.style.width = `${projectProgress.percentage}%`;
        dom.projectProgressText.textContent = `${projectProgress.percentage}% (${projectProgress.completed}/${projectProgress.total})`;

        // 設置任務列表容器為響應式網格佈局（類似專案看板）
        dom.todoTasksList.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4';
        dom.completedTasksList.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4';

        dom.todoTasksList.innerHTML = '';
        dom.completedTasksList.innerHTML = '';

        const todoTasks = project.tasks.filter(task => !task.completed);
        const completedTasks = project.tasks.filter(task => task.completed);

        // 渲染待辦任務
        if (todoTasks.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'col-span-full flex items-center justify-center h-32 text-gray-500 text-center';
            emptyMessage.innerHTML = `
                <div>
                    <i class="fas fa-tasks text-4xl mb-2 opacity-50"></i>
                    <p>暫無待辦任務</p>
                    <p class="text-sm mt-1">使用上方的輸入框新增第一個任務</p>
                </div>
            `;
            dom.todoTasksList.appendChild(emptyMessage);
        } else {
            todoTasks.forEach(task => {
                dom.todoTasksList.appendChild(createTaskCard(task));
            });
        }

        // 渲染已完成任務
        if (completedTasks.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'col-span-full flex items-center justify-center h-32 text-gray-500 text-center';
            emptyMessage.innerHTML = `
                <div>
                    <i class="fas fa-check-circle text-4xl mb-2 opacity-50"></i>
                    <p>暫無已完成任務</p>
                    <p class="text-sm mt-1">完成待辦任務後會顯示在這裡</p>
                </div>
            `;
            dom.completedTasksList.appendChild(emptyMessage);
        } else {
            completedTasks.forEach(task => {
                dom.completedTasksList.appendChild(createTaskCard(task));
            });
        }

        // 更新選擇狀態
        if (selectionManager) {
            selectionManager.updateUI();
        }
    };

    const renderStatistics = () => {
        if (statisticsManager) {
            statisticsManager.updateStatistics();
        }
    };

    const renderProjectSelector = () => {
        const state = stateManager.getState();
        
        dom.projectSelector.innerHTML = '';
        if (state.projects.length === 0) {
            const option = document.createElement('option');
            option.textContent = '尚無專案';
            option.disabled = true;
            dom.projectSelector.appendChild(option);
        } else {
            state.projects.forEach(p => {
                const option = document.createElement('option');
                option.value = p.id;
                option.textContent = p.name;
                if (p.id === state.currentProjectId) {
                    option.selected = true;
                }
                dom.projectSelector.appendChild(option);
            });
        }
    };

    const renderSettings = () => {
        const state = stateManager.getState();
        dom.apiKeyInput.value = state.apiKey || '';
    };

    // --- 專案和任務管理函數 ---
    const addProject = (name) => {
        const newProject = { 
            id: `proj-${Date.now()}`, 
            name, 
            description: '',
            tags: [],
            creationDate: new Date().toISOString(),
            tasks: [] 
        };
        
        const state = stateManager.getState();
        stateManager.setState({
            projects: [...state.projects, newProject],
            currentProjectId: newProject.id,
            activeTab: 'task-board'
        });
        
        Toast.success(`專案「${name}」已建立`);
        render();
    };

    const addTask = (text) => {
        const state = stateManager.getState();
        const project = state.projects.find(p => p.id === state.currentProjectId);
        
        if (project) {
            const newTask = {
                id: `task-${Date.now()}`,
                text,
                description: '',
                priority: 'medium',
                tags: [],
                completed: false,
                expanded: true,
                subtaskFilter: 'all',
                creationDate: new Date().toISOString(),
                completionDate: null,
                subtasks: []
            };
            
            project.tasks.push(newTask);
            stateManager.setState({ projects: [...state.projects] });
            
            Toast.success(`任務「${text}」已新增`);
            render();
        }
    };

    const addSubtask = (taskId, text) => {
        const state = stateManager.getState();
        const project = state.projects.find(p => p.id === state.currentProjectId);
        const task = project?.tasks.find(t => t.id === taskId);

        if (task) {
            const newSubtask = {
                id: `sub-${Date.now()}`,
                text,
                completed: false,
                creationDate: new Date().toISOString(),
                completionDate: null
            };

            task.subtasks.push(newSubtask);

            // 檢查並更新主任務的完成狀態
            updateTaskCompletionStatus(task);

            stateManager.setState({ projects: [...state.projects] });
            render();
        }
    };

    const toggleTaskProperty = (taskId, property) => {
        const state = stateManager.getState();
        const project = state.projects.find(p => p.id === state.currentProjectId);
        const task = project?.tasks.find(t => t.id === taskId);

        if (!task) return;

        if (property === 'completed') {
            // 主任務的完成狀態現在由子任務決定，不允許直接切換
            if (task.subtasks && task.subtasks.length > 0) {
                Toast.warning('有子任務的主任務完成狀態由子任務決定，請直接操作子任務');
                return;
            }

            // 只有沒有子任務的任務才能直接切換完成狀態
            task.completed = !task.completed;
            task.completionDate = task.completed ? new Date().toISOString() : null;

            const action = task.completed ? '完成' : '取消完成';
            Toast.success(`任務已${action}`);
        } else if (property === 'expanded') {
            task.expanded = !task.expanded;
        }

        stateManager.setState({ projects: [...state.projects] });
        render();
    };

    const setSubtaskFilter = (taskId, filter) => {
        const state = stateManager.getState();
        const project = state.projects.find(p => p.id === state.currentProjectId);
        const task = project?.tasks.find(t => t.id === taskId);

        if (task) {
            task.subtaskFilter = filter;
            stateManager.setState({ projects: [...state.projects] });
            render();
        }
    };

    const toggleSubtaskCompletion = (taskId, subtaskId) => {
        const state = stateManager.getState();
        const project = state.projects.find(p => p.id === state.currentProjectId);
        const task = project?.tasks.find(t => t.id === taskId);
        const subtask = task?.subtasks.find(st => st.id === subtaskId);

        if (subtask) {
            subtask.completed = !subtask.completed;
            subtask.completionDate = subtask.completed ? new Date().toISOString() : null;

            // 檢查並更新主任務的完成狀態
            const statusChanged = updateTaskCompletionStatus(task);

            stateManager.setState({ projects: [...state.projects] });

            if (statusChanged) {
                const action = task.completed ? '完成' : '取消完成';
                Toast.info(`主任務已自動${action}（基於子任務狀態）`);
            }

            render();
        }
    };

    // --- 任務編輯功能 (V4.4新增) ---
    const openEditTaskModal = (taskId) => {
        const state = stateManager.getState();
        const project = state.projects.find(p => p.id === state.currentProjectId);
        const task = project?.tasks.find(t => t.id === taskId);

        if (!task) {
            Toast.error('找不到指定的任務');
            return;
        }

        const content = `
            <form id="edit-task-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">任務標題 <span class="text-red-500">*</span></label>
                    <input type="text" id="edit-task-title" value="${task.text}" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="請輸入任務標題">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">任務詳述</label>
                    <textarea id="edit-task-description" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                              placeholder="輸入任務的詳細描述...">${task.description || ''}</textarea>
                </div>
            </form>
        `;

        new Modal({
            title: '編輯任務',
            content,
            size: 'lg',
            buttons: [
                {
                    id: 'delete',
                    text: '刪除任務',
                    type: 'danger',
                    onClick: () => confirmDeleteTask(taskId)
                },
                {
                    id: 'cancel',
                    text: '取消',
                    type: 'secondary'
                },
                {
                    id: 'save',
                    text: '儲存',
                    type: 'primary',
                    onClick: () => saveTaskEdit(taskId)
                }
            ],
            onOpen: () => {
                setTimeout(() => {
                    const titleInput = document.getElementById('edit-task-title');
                    if (titleInput) {
                        titleInput.focus();
                        titleInput.select();
                    }
                }, 100);
            }
        }).open();
    };

    const saveTaskEdit = (taskId) => {
        const titleInput = document.getElementById('edit-task-title');
        const descriptionInput = document.getElementById('edit-task-description');

        if (!titleInput || !descriptionInput) {
            Toast.error('表單元素未找到');
            return false;
        }

        const title = titleInput.value.trim();
        const description = descriptionInput.value.trim();

        if (!title) {
            Toast.error('任務標題不能為空');
            titleInput.focus();
            return false;
        }

        const state = stateManager.getState();
        const project = state.projects.find(p => p.id === state.currentProjectId);
        const task = project?.tasks.find(t => t.id === taskId);

        if (task) {
            const oldTitle = task.text;
            task.text = title;
            task.description = description;

            stateManager.setState({ projects: [...state.projects] });
            render();

            Toast.success(`任務「${oldTitle}」已更新`);
            return true;
        } else {
            Toast.error('找不到指定的任務');
            return false;
        }
    };

    const confirmDeleteTask = (taskId) => {
        const state = stateManager.getState();
        const project = state.projects.find(p => p.id === state.currentProjectId);
        const task = project?.tasks.find(t => t.id === taskId);

        if (!task) {
            Toast.error('找不到指定的任務');
            return false;
        }

        Modal.confirm(
            '確認刪除',
            `確定要刪除任務「${task.text}」嗎？${task.subtasks && task.subtasks.length > 0 ? `\n\n此任務包含 ${task.subtasks.length} 個子任務，刪除後將無法復原。` : '\n\n此操作無法復原。'}`,
            () => {
                deleteTask(taskId);
                Toast.success(`任務「${task.text}」已刪除`);
                return true;
            }
        );

        return false;
    };

    const deleteTask = (taskId) => {
        const state = stateManager.getState();
        const project = state.projects.find(p => p.id === state.currentProjectId);

        if (project) {
            project.tasks = project.tasks.filter(t => t.id !== taskId);
            stateManager.setState({ projects: [...state.projects] });
            render();
        }
    };

    // --- 工具函數 ---
    const calculateTaskProgress = (task) => {
        if (!task.subtasks || task.subtasks.length === 0) {
            // 沒有子任務的任務：完成度為0%或100%
            return {
                completed: task.completed ? 1 : 0,
                total: 1,
                percentage: task.completed ? 100 : 0
            };
        }

        const total = task.subtasks.length;
        const completed = task.subtasks.filter(st => st.completed).length;
        const percentage = total === 0 ? 0 : Math.round((completed / total) * 100);

        return { completed, total, percentage };
    };

    const calculateProjectProgress = (project) => {
        if (!project || project.tasks.length === 0) {
            return { percentage: 0, completed: 0, total: 0, taskCompletionRates: [] };
        }

        const taskCompletionRates = project.tasks.map(task => {
            const taskProgress = calculateTaskProgress(task);
            return taskProgress.percentage;
        });

        // 專案完成度 = 所有任務完成度的平均值
        const averageCompletion = taskCompletionRates.length === 0 ? 0 :
            Math.round(taskCompletionRates.reduce((sum, rate) => sum + rate, 0) / taskCompletionRates.length);

        // 完全完成的任務數量
        const fullyCompletedTasks = project.tasks.filter(t => t.completed).length;

        return {
            percentage: averageCompletion,
            completed: fullyCompletedTasks,
            total: project.tasks.length,
            taskCompletionRates
        };
    };

    // 檢查並更新主任務的完成狀態（基於子任務）
    const updateTaskCompletionStatus = (task) => {
        if (!task.subtasks || task.subtasks.length === 0) {
            // 沒有子任務的任務保持原狀態
            return task.completed;
        }

        const allSubtasksCompleted = task.subtasks.every(subtask => subtask.completed);
        const anySubtaskCompleted = task.subtasks.some(subtask => subtask.completed);

        const wasCompleted = task.completed;

        if (allSubtasksCompleted && task.subtasks.length > 0) {
            // 所有子任務都完成了，主任務應該標記為完成
            task.completed = true;
            if (!task.completionDate) {
                task.completionDate = new Date().toISOString();
            }
        } else {
            // 只要有任何子任務未完成，主任務就應該標記為未完成
            task.completed = false;
            task.completionDate = null;
        }

        return task.completed !== wasCompleted; // 返回狀態是否有變化
    };

    // --- DOM 元素創建函數 ---
    const createProjectCard = (project) => {
        const card = document.createElement('div');
        const progress = calculateProjectProgress(project);
        const status = progress.percentage === 100 ? '已完成' : '執行中';
        const statusColor = progress.percentage === 100 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';

        card.className = 'bg-white p-6 rounded-lg shadow-md flex flex-col hover:shadow-lg transition-shadow duration-200';
        card.innerHTML = `
            <div class="flex justify-between items-start mb-2">
                <h3 class="text-xl font-bold text-gray-800">${project.name}</h3>
                <div class="flex items-center space-x-2">
                    <span class="${statusColor} text-xs font-medium px-2.5 py-0.5 rounded-full">${status}</span>
                    <button data-action="delete-project" data-project-id="${project.id}"
                            class="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 rounded transition-colors duration-200"
                            title="刪除專案">
                        <i class="fas fa-trash text-xs"></i>
                    </button>
                </div>
            </div>
            <p class="text-sm text-gray-500 mb-4">${progress.completed} / ${progress.total} 項任務完成 (平均完成度: ${progress.percentage}%)</p>
            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                <div class="bg-blue-500 h-2.5 rounded-full transition-all duration-300" style="width: ${progress.percentage}%"></div>
            </div>
            <button data-action="view-project" data-project-id="${project.id}" class="mt-auto w-full bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-black transition-colors duration-200">
                <i class="fas fa-eye mr-2"></i>查看專案
            </button>
        `;
        return card;
    };

    const createTaskCard = (task) => {
        const card = document.createElement('div');
        const progress = calculateTaskProgress(task);

        // 格式化進度顯示
        let progressText = '';
        if (task.subtasks && task.subtasks.length > 0) {
            progressText = `(${progress.completed}/${progress.total}) ${progress.percentage}%`;
        } else if (task.completed) {
            progressText = '100%';
        }

        const creationDate = new Date(task.creationDate).toLocaleDateString();
        const completionDate = task.completionDate ? new Date(task.completionDate).toLocaleDateString() : '';

        card.className = `task-card bg-white p-3 rounded-lg shadow-md transition-all duration-200 cursor-pointer hover:bg-gray-50 hover:shadow-lg ${task.completed ? 'opacity-70' : ''}`;
        card.dataset.taskId = task.id;

        // 為沒有子任務的任務添加點擊事件到整個卡片
        if (!task.subtasks || task.subtasks.length === 0) {
            card.dataset.action = 'toggle-complete';
        }
        card.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    ${task.subtasks && task.subtasks.length > 0 ?
                        // 有子任務：顯示圖標和狀態標籤
                        `<div class="flex items-center space-x-2">
                            <i class="fas fa-tasks text-blue-500"></i>
                            <h4 class="font-bold text-lg ${task.completed ? 'line-through text-gray-500' : ''}">${task.text}</h4>
                        </div>` :
                        // 沒有子任務：整個卡片可點擊
                        `<input type="checkbox" data-action="toggle-complete" class="hidden" ${task.completed ? 'checked' : ''}>
                         <div class="flex items-center space-x-2">
                             <div class="w-5 h-5 border-2 border-blue-500 rounded flex items-center justify-center ${task.completed ? 'bg-blue-500' : 'bg-white'}">
                                 ${task.completed ? '<i class="fas fa-check text-white text-xs"></i>' : ''}
                             </div>
                             <h4 class="font-bold text-lg ${task.completed ? 'line-through text-gray-500' : ''}">${task.text}</h4>
                         </div>`
                    }
                    <span class="text-sm text-gray-500">${progressText}</span>
                    ${task.completed ? '<span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">已完成</span>' : ''}
                </div>
                <div class="flex items-center space-x-2">
                    <button data-action="edit-task" class="text-gray-400 hover:text-blue-500 transition-colors duration-200 p-1 rounded hover:bg-gray-100" title="編輯任務">
                        <i class="fas fa-edit text-sm"></i>
                    </button>
                    <button data-action="toggle-expand" class="text-gray-500 hover:text-blue-500 transition-colors duration-200">
                        <i class="fas fa-chevron-down transition-transform ${task.expanded ? 'rotate-180' : ''}"></i>
                    </button>
                </div>
            </div>
            ${task.description ? `<div class="text-sm text-gray-600 mt-2 ml-8 leading-relaxed">${task.description}</div>` : ''}
            <div class="text-xs text-gray-400 mt-2 ml-8">
                <div>創建於: ${creationDate}</div>
                ${completionDate ? `<div>完成於: ${completionDate}</div>` : ''}
            </div>
            ${task.subtasks && task.subtasks.length > 0 ? `
                <div class="mt-3 ml-8">
                    <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                        <span>進度</span>
                        <span>${progress.percentage}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1.5">
                        <div class="bg-blue-500 h-1.5 rounded-full transition-all duration-300" style="width: ${progress.percentage}%"></div>
                    </div>
                </div>
            ` : ''}
            <div data-subtask-container class="mt-4 pl-8 ${task.expanded ? '' : 'hidden'}">
                <div class="flex items-center justify-end space-x-2 mb-2">
                    <span class="text-xs font-semibold">篩選:</span>
                    <button data-action="filter-subtasks" data-filter="all" class="subtask-filter-btn px-2 py-0.5 text-xs rounded-md transition-colors duration-200">全部</button>
                    <button data-action="filter-subtasks" data-filter="todo" class="subtask-filter-btn px-2 py-0.5 text-xs rounded-md transition-colors duration-200">待辦</button>
                    <button data-action="filter-subtasks" data-filter="completed" class="subtask-filter-btn px-2 py-0.5 text-xs rounded-md transition-colors duration-200">已完成</button>
                </div>
                
                <form data-action="add-subtask" class="flex mt-2">
                    <input type="text" class="flex-1 p-1 text-sm border rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="新增子任務..." required>
                    <button type="submit" class="bg-gray-200 text-gray-700 px-3 rounded-r-md hover:bg-gray-300 transition-colors duration-200">+</button>
                </form>
                <div class="space-y-2 mt-2" data-subtask-list></div>
            </div>
        `;

        const subtaskListContainer = card.querySelector('[data-subtask-list]');
        const filteredSubtasks = task.subtasks.filter(st => {
            if (task.subtaskFilter === 'all') return true;
            if (task.subtaskFilter === 'todo') return !st.completed;
            if (task.subtaskFilter === 'completed') return st.completed;
            return false;
        });

        if (filteredSubtasks.length > 0) {
            filteredSubtasks.forEach(st => subtaskListContainer.appendChild(createSubtaskElement(st)));
        } else {
            subtaskListContainer.innerHTML = '<p class="text-xs text-gray-400 text-center">沒有符合條件的子任務。</p>';
        }

        card.querySelectorAll('.subtask-filter-btn').forEach(btn => {
            if (btn.dataset.filter === task.subtaskFilter) {
                btn.classList.add('bg-blue-500', 'text-white');
            }
        });

        return card;
    };

    const createSubtaskElement = (subtask) => {
        const el = document.createElement('div');
        const creationDate = new Date(subtask.creationDate).toLocaleDateString();
        const completionDate = subtask.completionDate ? new Date(subtask.completionDate).toLocaleDateString() : '';

        el.className = `p-1.5 rounded-md flex flex-col transition-all duration-200 cursor-pointer hover:bg-gray-200 ${subtask.completed ? 'opacity-60 bg-gray-50' : 'bg-gray-100'}`;
        el.dataset.subtaskId = subtask.id;
        el.dataset.action = 'toggle-subtask-complete';
        el.innerHTML = `
            <div class="flex items-center space-x-2">
                <input type="checkbox" data-action="toggle-subtask-complete" class="hidden" ${subtask.completed ? 'checked' : ''}>
                <div class="w-3 h-3 border-2 border-blue-500 rounded flex items-center justify-center ${subtask.completed ? 'bg-blue-500' : 'bg-white'}">
                    ${subtask.completed ? '<i class="fas fa-check text-white text-xs"></i>' : ''}
                </div>
                <span class="text-xs ${subtask.completed ? 'line-through text-gray-500' : ''}">${subtask.text}</span>
            </div>
            <div class="text-xs text-gray-400 mt-1 ml-5">
                <div>創建: ${creationDate}</div>
                ${completionDate ? `<div>完成: ${completionDate}</div>` : ''}
            </div>
        `;
        return el;
    };

    // --- AI 助理功能 ---
    const addMessageToChat = (sender, message) => {
        const msgDiv = document.createElement('div');
        msgDiv.className = `text-sm p-2 rounded-lg mb-2 max-w-xs ${sender === 'user' ? 'bg-blue-100 self-end' : 'bg-gray-200 self-start'}`;
        msgDiv.innerHTML = message;
        dom.chatHistory.appendChild(msgDiv);
        dom.chatHistory.scrollTop = dom.chatHistory.scrollHeight;
    };

    const callGeminiAPI = async (prompt, retryCount = 0) => {
        const state = stateManager.getState();
        const maxRetries = 3;

        // 檢查API金鑰
        if (!state.apiKey || state.apiKey.trim() === '') {
            addMessageToChat('user', prompt);
            addMessageToChat('ai', `請先在設定頁面設置您的 Gemini API 金鑰。<br><small class="text-blue-500">點擊右上角的設定按鈕進行設置</small>`);
            return;
        }

        if (retryCount === 0) {
            addMessageToChat('user', prompt);
            addMessageToChat('ai', '<i class="fas fa-spinner fa-spin"></i> 思考中...');
        } else {
            // 更新載入訊息顯示重試狀態
            const lastMessage = dom.chatHistory.lastChild;
            if (lastMessage) {
                lastMessage.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 重試中... (${retryCount}/${maxRetries})`;
            }
        }

        const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${state.apiKey}`;
        const fullPrompt = `你是一個任務管理助理。根據使用者的要求，回傳一個 JSON 物件。JSON 必須包含 'action' (例如 'addTask', 'addProjectPlan') 和 'payload')。\n\n模式一：快速新增。若使用者指令單純，回傳單一動作 JSON。例如使用者說：「新增任務：買牛奶」，你回傳：{"action": "addTask", "payload": {"text": "買牛奶"}}。\n\n模式二：完整規劃。若使用者要求規劃，請回傳一個符合我們應用程式資料結構的完整專案物件 JSON。專案物件應包含 id, name, 和一個 tasks 陣列，tasks陣列中可包含subtasks。例如使用者說：「規劃一個為期一週的東京旅遊」，你回傳：{"action": "addProjectPlan", "payload": {"id": "proj-${Date.now()}", "name": "東京一週旅遊", "tasks": [{"id":"task-${Date.now() + 1}","text":"行前準備","completed":false,"expanded":true,"subtaskFilter":"all","creationDate":"${new Date().toISOString()}","completionDate":null,"subtasks":[{"id":"sub-${Date.now() + 2}","text":"購買機票","completed":false,"creationDate":"${new Date().toISOString()}","completionDate":null}]}]}}\n\n使用者說：「${prompt}」`;

        try {
            const response = await fetch(API_URL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ contents: [{ parts: [{ text: fullPrompt }] }] })
            });

            // 移除載入訊息
            if (dom.chatHistory.lastChild) {
                dom.chatHistory.removeChild(dom.chatHistory.lastChild);
            }

            if (!response.ok) {
                let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

                try {
                    const errorData = await response.json();
                    if (errorData.error && errorData.error.message) {
                        errorMessage = errorData.error.message;
                    }
                } catch (e) {
                    // 如果無法解析錯誤JSON，使用預設訊息
                }

                // 檢查是否為可重試的錯誤
                if ((response.status === 503 || response.status === 429 || response.status >= 500) && retryCount < maxRetries) {
                    console.log(`API請求失敗，準備重試 (${retryCount + 1}/${maxRetries}):`, errorMessage);

                    // 等待一段時間後重試（指數退避）
                    const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
                    await new Promise(resolve => setTimeout(resolve, delay));

                    return callGeminiAPI(prompt, retryCount + 1);
                }

                throw new Error(errorMessage);
            }

            const data = await response.json();

            // 檢查回應結構
            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content || !data.candidates[0].content.parts || !data.candidates[0].content.parts[0]) {
                throw new Error("API 回應格式異常");
            }

            const aiResponseText = data.candidates[0].content.parts[0].text;
            const jsonMatch = aiResponseText.match(/\`\`\`json([\s\S]*?)\`\`\`/);

            if (!jsonMatch || !jsonMatch[1]) {
                // 如果沒有找到JSON格式，嘗試直接解析整個回應
                try {
                    const command = JSON.parse(aiResponseText.trim());
                    executeAIAction(command);
                    return;
                } catch (e) {
                    throw new Error("AI 未回傳有效的 JSON 格式");
                }
            }

            const command = JSON.parse(jsonMatch[1]);
            executeAIAction(command);

        } catch (error) {
            console.error('Gemini API Error:', error);

            // 根據錯誤類型提供不同的用戶提示
            let userMessage = '抱歉，AI 功能出現錯誤。';
            let suggestion = '';

            if (error.message.includes('overloaded') || error.message.includes('503')) {
                userMessage = 'Gemini API 目前負載過高，請稍後再試。';
                suggestion = '建議等待幾分鐘後重新嘗試';
            } else if (error.message.includes('API_KEY_INVALID') || error.message.includes('401')) {
                userMessage = 'API 金鑰無效，請檢查設定。';
                suggestion = '請到設定頁面重新設置正確的 API 金鑰';
            } else if (error.message.includes('QUOTA_EXCEEDED') || error.message.includes('429')) {
                userMessage = 'API 使用額度已達上限。';
                suggestion = '請檢查您的 Google Cloud 帳戶配額';
            } else if (error.message.includes('JSON')) {
                userMessage = 'AI 回應格式異常，請重新嘗試。';
                suggestion = '可以嘗試重新描述您的需求';
            }

            addMessageToChat('ai', `${userMessage}<br><small class="text-red-500">${error.message}</small>${suggestion ? `<br><small class="text-blue-500">${suggestion}</small>` : ''}`);
        }
    };

    const executeAIAction = (command) => {
        if (!command || !command.action) return;

        const state = stateManager.getState();

        switch (command.action) {
            case 'addTask':
                if (state.currentProjectId) {
                    addTask(command.payload.text);
                    addMessageToChat('ai', `好的，已在目前專案新增任務：「${command.payload.text}」。`);
                } else {
                    addMessageToChat('ai', '請先選擇一個專案才能新增任務。');
                }
                break;
            case 'addProjectPlan':
                const newProject = { ...command.payload, id: `proj-${Date.now()}` };
                stateManager.setState({
                    projects: [...state.projects, newProject],
                    currentProjectId: newProject.id,
                    activeTab: 'task-board'
                });
                addMessageToChat('ai', `好的，已為您建立新專案：「${newProject.name}」。`);
                render();
                break;
            default:
                addMessageToChat('ai', '抱歉，我無法理解這個指令。');
        }
    };

    // --- 事件監聽器 ---
    const addEventListeners = () => {
        // 快速操作按鈕
        if (dom.quickActionBtn) {
            dom.quickActionBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (shortcutManager) {
                    shortcutManager.showQuickActionList();
                } else {
                    Toast.warning('快速操作功能正在載入中，請稍後再試');
                }
            });
        }

        // 分頁切換
        dom.tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                stateManager.setState({ activeTab: btn.dataset.tab });
                render();
            });
        });

        // 專案看板篩選
        if (dom.projectFilterButtons) {
            dom.projectFilterButtons.addEventListener('click', (e) => {
                if (e.target.tagName === 'BUTTON') {
                    stateManager.setState({ projectBoardFilter: e.target.dataset.filter });
                    render();
                }
            });
        }

        // 專案看板 - 查看專案和刪除專案
        if (dom.projectBoardTabContent) {
            dom.projectBoardTabContent.addEventListener('click', (e) => {
                const viewButton = e.target.closest('[data-action="view-project"]');
                const deleteButton = e.target.closest('[data-action="delete-project"]');

                if (viewButton) {
                    stateManager.setState({
                        currentProjectId: viewButton.dataset.projectId,
                        activeTab: 'task-board'
                    });
                    render();
                } else if (deleteButton) {
                    e.stopPropagation(); // 防止事件冒泡
                    const projectId = deleteButton.dataset.projectId;
                    const state = stateManager.getState();
                    const project = state.projects.find(p => p.id === projectId);

                    if (project) {
                        // 確認刪除對話框
                        const taskCount = project.tasks ? project.tasks.length : 0;
                        const confirmMessage = taskCount > 0
                            ? `確定要刪除專案「${project.name}」嗎？\n\n此專案包含 ${taskCount} 個任務，刪除後將無法復原。`
                            : `確定要刪除專案「${project.name}」嗎？\n\n刪除後將無法復原。`;

                        if (confirm(confirmMessage)) {
                            // 刪除專案
                            const updatedProjects = state.projects.filter(p => p.id !== projectId);

                            // 如果刪除的是當前專案，清除當前專案ID
                            const newState = {
                                projects: updatedProjects
                            };

                            if (state.currentProjectId === projectId) {
                                newState.currentProjectId = updatedProjects.length > 0 ? updatedProjects[0].id : null;
                            }

                            stateManager.setState(newState);
                            render();

                            // 顯示成功訊息
                            if (typeof Toast !== 'undefined') {
                                Toast.success(`專案「${project.name}」已刪除`);
                            } else {
                                alert(`專案「${project.name}」已刪除`);
                            }
                        }
                    }
                }
            });
        }

        // 專案選擇器
        if (dom.projectSelector) {
            dom.projectSelector.addEventListener('change', (e) => {
                stateManager.setState({ currentProjectId: e.target.value });
                render();
            });
        }

        // 新增專案
        if (dom.addProjectForm) {
            dom.addProjectForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const name = dom.newProjectInput.value.trim();
                if (name) {
                    addProject(name);
                    dom.newProjectInput.value = '';
                }
            });
        }

        // 新增任務
        if (dom.addTaskForm) {
            dom.addTaskForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const text = dom.newTaskInput.value.trim();
                if (text) {
                    addTask(text);
                    dom.newTaskInput.value = '';
                }
            });
        }

        // 檢查是否應該忽略點擊（點擊的是控制元素）
        const shouldIgnoreClick = (target) => {
            // 檢查元素類型
            const tagName = target.tagName.toLowerCase();
            const inputTypes = ['button', 'input', 'textarea', 'select', 'a'];

            if (inputTypes.includes(tagName)) {
                return true;
            }

            // 檢查是否有特定的 data-action（除了 toggle-complete 和 toggle-subtask-complete）
            const action = target.dataset.action;
            if (action && action !== 'toggle-complete' && action !== 'toggle-subtask-complete') {
                return true;
            }

            // 檢查是否在特定的控制區域內
            if (target.closest('button, input, textarea, select, a, [data-action="toggle-expand"], [data-action="filter-subtasks"], .subtask-input-container')) {
                return true;
            }

            return false;
        };

        // 任務看板事件
        if (dom.taskBoardTabContent) {
            dom.taskBoardTabContent.addEventListener('click', (e) => {
                const action = e.target.dataset.action;

                // 處理有明確 action 的點擊
                if (action) {
                    const taskCard = e.target.closest('.task-card');
                    if (!taskCard) return;
                    const taskId = taskCard.dataset.taskId;

                    if (action === 'toggle-complete') {
                        toggleTaskProperty(taskId, 'completed');
                    } else if (action === 'toggle-expand') {
                        toggleTaskProperty(taskId, 'expanded');
                    } else if (action === 'edit-task') {
                        openEditTaskModal(taskId);
                    } else if (action === 'toggle-subtask-complete') {
                        const subtaskEl = e.target.closest('[data-subtask-id]');
                        if (subtaskEl) {
                            toggleSubtaskCompletion(taskId, subtaskEl.dataset.subtaskId);
                        }
                    } else if (action === 'filter-subtasks') {
                        setSubtaskFilter(taskId, e.target.dataset.filter);
                    }
                    return;
                }

                // 處理沒有明確 action 的點擊（事件冒泡）
                if (shouldIgnoreClick(e.target)) {
                    return;
                }

                // 檢查是否點擊在子任務元素上
                const subtaskEl = e.target.closest('[data-subtask-id]');
                if (subtaskEl && subtaskEl.dataset.action === 'toggle-subtask-complete') {
                    const taskCard = e.target.closest('.task-card');
                    if (taskCard) {
                        const taskId = taskCard.dataset.taskId;
                        const subtaskId = subtaskEl.dataset.subtaskId;
                        toggleSubtaskCompletion(taskId, subtaskId);
                    }
                    return;
                }

                // 檢查是否點擊在任務卡片上
                const taskCard = e.target.closest('.task-card');
                if (taskCard && taskCard.dataset.action === 'toggle-complete') {
                    const taskId = taskCard.dataset.taskId;
                    toggleTaskProperty(taskId, 'completed');
                }
            });

            dom.taskBoardTabContent.addEventListener('submit', (e) => {
                e.preventDefault();
                if (e.target.dataset.action === 'add-subtask') {
                    const taskCard = e.target.closest('.task-card');
                    const taskId = taskCard.dataset.taskId;
                    const input = e.target.querySelector('input');
                    const text = input.value.trim();
                    if (text) {
                        addSubtask(taskId, text);
                        input.value = '';
                    }
                }
            });
        }

        // 設定頁面事件
        if (dom.saveApiKeyBtn) {
            dom.saveApiKeyBtn.addEventListener('click', () => {
                const key = dom.apiKeyInput.value.trim();
                stateManager.setState({ apiKey: key || null });
                render();
                Toast.success(key ? 'API 金鑰已儲存' : 'API 金鑰已清除');
            });
        }

        if (dom.exportJsonBtn) {
            dom.exportJsonBtn.addEventListener('click', () => {
                const dataStr = stateManager.exportState();
                const blob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'project-backup-v4.3.json';
                a.click();
                URL.revokeObjectURL(url);
                Toast.success('資料已匯出');
            });
        }

        if (dom.importJsonInput) {
            dom.importJsonInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = (event) => {
                    try {
                        const success = stateManager.importState(event.target.result);
                        if (success) {
                            Toast.success('匯入成功！頁面將會重新整理。');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            Toast.error('檔案格式不符！');
                        }
                    } catch (err) {
                        Toast.error('讀取檔案失敗，請確認檔案為正確的 JSON 格式。');
                        console.error(err);
                    }
                };
                reader.readAsText(file);
                e.target.value = '';
            });
        }

        // AI 聊天
        if (dom.chatForm) {
            dom.chatForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const userInput = dom.chatInput.value.trim();
                const state = stateManager.getState();

                if (userInput && state.apiKey) {
                    callGeminiAPI(userInput);
                    dom.chatInput.value = '';
                }
            });
        }

        // 搜尋選擇事件
        document.addEventListener('searchSelect', (e) => {
            const { result } = e.detail;

            if (result.type === 'project') {
                // 跳轉到任務看板並設置當前專案（顯示專案的任務列表）
                stateManager.setState({
                    activeTab: 'task-board',
                    currentProjectId: result.id
                });

            } else if (result.type === 'task' || result.type === 'subtask') {
                // 跳轉到任務看板並設置當前專案
                stateManager.setState({
                    currentProjectId: result.projectId,
                    activeTab: 'task-board'
                });

                // 滾動到對應的任務卡片
                setTimeout(() => {
                    const taskCard = document.querySelector(`[data-task-id="${result.type === 'task' ? result.id : result.parentId}"]`);
                    if (taskCard) {
                        taskCard.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });
                        // 添加高亮效果
                        taskCard.classList.add('ring-2', 'ring-blue-500', 'ring-opacity-50');
                        setTimeout(() => {
                            taskCard.classList.remove('ring-2', 'ring-blue-500', 'ring-opacity-50');
                        }, 2000);

                        // 如果是子任務，展開父任務
                        if (result.type === 'subtask') {
                            const expandBtn = taskCard.querySelector('[data-action="toggle-expand"]');
                            if (expandBtn && !taskCard.querySelector('[data-subtask-container]').classList.contains('hidden')) {
                                // 任務已經展開，直接高亮子任務
                                const subtaskEl = taskCard.querySelector(`[data-subtask-id="${result.id}"]`);
                                if (subtaskEl) {
                                    setTimeout(() => {
                                        subtaskEl.classList.add('ring-2', 'ring-green-500', 'ring-opacity-50');
                                        setTimeout(() => {
                                            subtaskEl.classList.remove('ring-2', 'ring-green-500', 'ring-opacity-50');
                                        }, 2000);
                                    }, 500);
                                }
                            } else if (expandBtn) {
                                // 展開任務以顯示子任務
                                expandBtn.click();
                                setTimeout(() => {
                                    const subtaskEl = taskCard.querySelector(`[data-subtask-id="${result.id}"]`);
                                    if (subtaskEl) {
                                        subtaskEl.classList.add('ring-2', 'ring-green-500', 'ring-opacity-50');
                                        setTimeout(() => {
                                            subtaskEl.classList.remove('ring-2', 'ring-green-500', 'ring-opacity-50');
                                        }, 2000);
                                    }
                                }, 500);
                            }
                        }
                    }
                }, 100);
            }

            render();
            Toast.info(`已跳轉到：${result.title}`);
        });

        // 自訂事件監聽
        document.addEventListener('selectAllTasks', () => {
            if (selectionManager) {
                selectionManager.selectAll();
            }
        });

        document.addEventListener('bulkDeleteTasks', () => {
            if (bulkOperationManager) {
                bulkOperationManager.bulkDelete();
            }
        });

        document.addEventListener('bulkCompleteTasks', () => {
            if (bulkOperationManager) {
                bulkOperationManager.bulkComplete();
            }
        });

        document.addEventListener('bulkMoveTasks', () => {
            if (bulkOperationManager) {
                bulkOperationManager.bulkMove();
            }
        });

        document.addEventListener('toggleSelectionMode', () => {
            if (selectionManager) {
                selectionManager.toggleSelectionMode();
            }
        });

        document.addEventListener('exitSelectionMode', () => {
            if (selectionManager) {
                selectionManager.exitSelectionMode();
            }
        });

        document.addEventListener('refreshProjectBoard', () => {
            render();
        });

        document.addEventListener('refreshTaskBoard', () => {
            render();
        });

        let isRefreshing = false;
        document.addEventListener('refreshStatistics', () => {
            if (statisticsManager && !isRefreshing) {
                isRefreshing = true;
                statisticsManager.updateStatistics();
                isRefreshing = false;
            }
        });

        document.addEventListener('exportData', () => {
            if (dom.exportJsonBtn) {
                dom.exportJsonBtn.click();
            }
        });
    };

    // --- 初始化應用程式 ---
    const init = () => {
        // 載入狀態
        const hasState = stateManager.loadState();

        if (!hasState) {
            // 初始化預設資料
            addProject('歡迎使用 V4.3！');
            stateManager.setState({ activeTab: 'project-board' });
            addTask('這是您的第一個專案！');
            addTask('點擊「設定」來配置您的 API 金鑰以啟用 AI 助理');
        }

        // 初始化管理器（在事件綁定之前）
        initializeManagers();

        // 綁定事件（在管理器初始化之後）
        addEventListeners();

        // 初始渲染
        render();

        // 顯示歡迎訊息
        setTimeout(() => {
            Toast.info('歡迎使用專案代辦事項管理 V4.3！', { duration: 5000 });
        }, 1000);
    };

    // 啟動應用程式
    init();
});
