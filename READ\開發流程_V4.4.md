# 開發流程 V4.4 - 任務詳述顯示與編輯功能

## 🚀 開發概述

**目標版本**: V4.4  
**開發時間**: 2025-08-07  
**主要功能**: 任務詳述顯示與編輯功能  
**開發方式**: 漸進式升級

## 📋 開發步驟

### 階段一：準備工作

#### 1.1 檔案備份
```bash
# 備份目標結構
backup/V4.4/
├── app-v4.4.js
├── index.html
├── js/
│   ├── components/
│   └── modules/
└── READ/
    ├── 功能規劃_V4.4.md
    └── 開發流程_V4.4.md
```

**執行步驟**:
1. 創建 `backup/V4.4/` 目錄
2. 複製當前主要檔案到備份目錄
3. 保持目錄結構完整

#### 1.2 版本號更新
**需要更新的檔案**:
- `app-v4.3.js` → `app-v4.4.js`
- `index.html` (檔案引用和註解)
- `READ/` 目錄下的相關文檔

**更新內容**:
- 檔案名稱中的版本號
- 程式碼註解中的版本標識
- HTML 檔案中的 script 引用
- 文檔標題和版本說明

### 階段二：核心功能實現

#### 2.1 任務詳述顯示功能

**修改檔案**: `app-v4.4.js`

**實現要點**:
```javascript
// 在 renderTaskCard 函數中添加詳述顯示邏輯
const renderTaskCard = (task) => {
    // 現有代碼...
    
    // 新增：詳述顯示邏輯
    const descriptionHtml = task.description ? 
        `<div class="text-sm text-gray-600 mt-1 ml-8">${task.description}</div>` : '';
    
    card.innerHTML = `
        <!-- 現有任務標題區域 -->
        ${descriptionHtml}
        <!-- 現有其他內容 -->
    `;
};
```

**技術細節**:
- 條件渲染：只在 `task.description` 存在且非空時顯示
- 樣式設計：較小字體、灰色、適當縮排
- 位置安排：在任務標題下方，日期資訊上方

#### 2.2 編輯圖示添加

**實現要點**:
```javascript
// 在任務卡片中添加編輯圖示
const editIconHtml = `
    <button data-action="edit-task" class="text-gray-400 hover:text-blue-500 transition-colors duration-200 p-1">
        <i class="fas fa-edit text-sm"></i>
    </button>
`;

// 將編輯圖示添加到卡片右上角
```

**技術細節**:
- 圖示位置：任務卡片右上角
- 樣式設計：灰色預設，hover 時藍色
- 事件處理：使用 data-action 屬性

#### 2.3 編輯模態框實現

**新增函數**:
```javascript
// 開啟編輯模態框
const openEditTaskModal = (taskId) => {
    const task = findTask(taskId);
    if (!task) return;
    
    const content = `
        <form id="edit-task-form">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">任務標題</label>
                <input type="text" id="edit-task-title" value="${task.text}" required 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">任務詳述</label>
                <textarea id="edit-task-description" rows="4" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="輸入任務的詳細描述...">${task.description || ''}</textarea>
            </div>
        </form>
    `;
    
    new Modal({
        title: '編輯任務',
        content,
        size: 'lg',
        buttons: [
            {
                id: 'delete',
                text: '刪除任務',
                type: 'danger',
                onClick: () => confirmDeleteTask(taskId)
            },
            {
                id: 'cancel',
                text: '取消',
                type: 'secondary'
            },
            {
                id: 'save',
                text: '儲存',
                type: 'primary',
                onClick: () => saveTaskEdit(taskId)
            }
        ],
        onOpen: () => {
            document.getElementById('edit-task-title').focus();
        }
    }).open();
};
```

#### 2.4 編輯功能實現

**新增函數**:
```javascript
// 儲存任務編輯
const saveTaskEdit = (taskId) => {
    const titleInput = document.getElementById('edit-task-title');
    const descriptionInput = document.getElementById('edit-task-description');
    
    const title = titleInput.value.trim();
    const description = descriptionInput.value.trim();
    
    if (!title) {
        Toast.error('任務標題不能為空');
        titleInput.focus();
        return false;
    }
    
    const state = stateManager.getState();
    const project = state.projects.find(p => p.id === state.currentProjectId);
    const task = project?.tasks.find(t => t.id === taskId);
    
    if (task) {
        task.text = title;
        task.description = description;
        
        stateManager.setState({ projects: [...state.projects] });
        render();
        
        Toast.success('任務已更新');
        return true;
    }
    
    return false;
};

// 確認刪除任務
const confirmDeleteTask = (taskId) => {
    const task = findTask(taskId);
    if (!task) return false;
    
    Modal.confirm(
        '確認刪除',
        `確定要刪除任務「${task.text}」嗎？此操作無法復原。`,
        () => {
            deleteTask(taskId);
            Toast.success('任務已刪除');
            return true;
        }
    );
    
    return false;
};

// 刪除任務
const deleteTask = (taskId) => {
    const state = stateManager.getState();
    const project = state.projects.find(p => p.id === state.currentProjectId);
    
    if (project) {
        project.tasks = project.tasks.filter(t => t.id !== taskId);
        stateManager.setState({ projects: [...state.projects] });
        render();
    }
};
```

### 階段三：事件處理

#### 3.1 編輯事件綁定

**修改位置**: 事件監聽器部分

```javascript
// 在任務看板事件處理中添加編輯功能
if (dom.taskBoardTabContent) {
    dom.taskBoardTabContent.addEventListener('click', (e) => {
        const action = e.target.dataset.action;
        
        if (action === 'edit-task') {
            const taskCard = e.target.closest('.task-card');
            if (taskCard) {
                const taskId = taskCard.dataset.taskId;
                openEditTaskModal(taskId);
            }
            return;
        }
        
        // 現有其他事件處理...
    });
}
```

#### 3.2 鍵盤快捷鍵支援

**可選實現**:
- ESC 鍵關閉編輯模態框
- Ctrl+S 快速儲存編輯

### 階段四：樣式調整

#### 4.1 CSS 樣式優化

**需要調整的樣式**:
```css
/* 詳述文字樣式 */
.task-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
    margin-left: 2rem;
    line-height: 1.4;
}

/* 編輯圖示樣式 */
.edit-task-btn {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.task-card:hover .edit-task-btn {
    opacity: 1;
}

/* 編輯表單樣式 */
.edit-task-form textarea {
    resize: vertical;
    min-height: 100px;
}
```

### 階段五：測試與驗證

#### 5.1 功能測試清單

**基本功能**:
- [ ] 詳述正確顯示/隱藏
- [ ] 編輯圖示正常顯示
- [ ] 編輯模態框正常開啟
- [ ] 表單預填資料正確
- [ ] 儲存功能正常
- [ ] 刪除功能正常
- [ ] 取消功能正常

**邊界情況**:
- [ ] 空標題驗證
- [ ] 長文本處理
- [ ] 特殊字符處理
- [ ] 網路錯誤處理

**兼容性測試**:
- [ ] 現有功能不受影響
- [ ] 子任務功能正常
- [ ] 搜尋功能正常
- [ ] 快捷鍵功能正常

#### 5.2 性能測試

**測試項目**:
- [ ] 大量任務時的渲染性能
- [ ] 編輯操作的響應速度
- [ ] 記憶體使用情況

## 🔧 技術實現細節

### 1. 資料結構保持不變
- 不修改現有任務物件結構
- 充分利用現有 `description` 欄位

### 2. 組件重用
- 使用現有 `Modal` 組件
- 使用現有 `Toast` 提示組件
- 保持現有狀態管理機制

### 3. 錯誤處理
- 表單驗證錯誤
- 資料儲存錯誤
- 網路連接錯誤

### 4. 性能優化
- 避免不必要的重新渲染
- 使用事件委派處理點擊事件
- 適當的防抖處理

## 📝 開發注意事項

1. **代碼品質**: 保持與現有代碼風格一致
2. **註解完整**: 為新增功能添加清晰註解
3. **錯誤處理**: 妥善處理各種異常情況
4. **用戶體驗**: 提供清晰的操作反饋
5. **向後兼容**: 確保不影響現有功能

## 🎯 完成標準

1. **功能完整**: 所有規劃功能正常運作
2. **測試通過**: 所有測試項目通過
3. **文檔更新**: 相關文檔更新完成
4. **代碼品質**: 代碼結構清晰，註解完整
5. **用戶體驗**: 操作流暢，反饋清晰
