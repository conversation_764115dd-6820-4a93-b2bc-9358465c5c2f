/**
 * Button Component - V4.3
 * 統一的按鈕組件，支援多種樣式和狀態
 */

class Button {
    constructor(options = {}) {
        this.options = {
            text: options.text || '',
            type: options.type || 'primary', // primary, secondary, danger, success, warning
            size: options.size || 'md', // sm, md, lg
            icon: options.icon || null,
            iconPosition: options.iconPosition || 'left', // left, right
            disabled: options.disabled || false,
            loading: options.loading || false,
            onClick: options.onClick || (() => {}),
            className: options.className || '',
            ...options
        };
    }

    render() {
        const button = document.createElement('button');
        button.className = this.getButtonClasses();
        button.innerHTML = this.getButtonContent();
        button.disabled = this.options.disabled || this.options.loading;
        
        // 綁定事件
        button.addEventListener('click', (e) => {
            if (!this.options.disabled && !this.options.loading) {
                this.options.onClick(e);
            }
        });

        return button;
    }

    getButtonClasses() {
        const baseClasses = [
            'inline-flex',
            'items-center',
            'justify-center',
            'font-medium',
            'rounded-lg',
            'transition-all',
            'duration-200',
            'focus:outline-none',
            'focus:ring-2',
            'focus:ring-offset-2'
        ];

        // 尺寸類
        const sizeClasses = {
            sm: ['px-3', 'py-1.5', 'text-sm'],
            md: ['px-4', 'py-2', 'text-sm'],
            lg: ['px-6', 'py-3', 'text-base']
        };

        // 類型類
        const typeClasses = {
            primary: [
                'bg-blue-500',
                'text-white',
                'hover:bg-blue-600',
                'focus:ring-blue-500',
                'disabled:bg-blue-300'
            ],
            secondary: [
                'bg-gray-200',
                'text-gray-900',
                'hover:bg-gray-300',
                'focus:ring-gray-500',
                'disabled:bg-gray-100',
                'disabled:text-gray-400'
            ],
            danger: [
                'bg-red-500',
                'text-white',
                'hover:bg-red-600',
                'focus:ring-red-500',
                'disabled:bg-red-300'
            ],
            success: [
                'bg-green-500',
                'text-white',
                'hover:bg-green-600',
                'focus:ring-green-500',
                'disabled:bg-green-300'
            ],
            warning: [
                'bg-yellow-500',
                'text-white',
                'hover:bg-yellow-600',
                'focus:ring-yellow-500',
                'disabled:bg-yellow-300'
            ],
            ghost: [
                'bg-transparent',
                'text-gray-700',
                'hover:bg-gray-100',
                'focus:ring-gray-500',
                'disabled:text-gray-400'
            ],
            outline: [
                'bg-transparent',
                'border',
                'border-gray-300',
                'text-gray-700',
                'hover:bg-gray-50',
                'focus:ring-gray-500',
                'disabled:text-gray-400',
                'disabled:border-gray-200'
            ]
        };

        const classes = [
            ...baseClasses,
            ...sizeClasses[this.options.size],
            ...typeClasses[this.options.type]
        ];

        // 載入狀態
        if (this.options.loading) {
            classes.push('cursor-not-allowed');
        }

        // 禁用狀態
        if (this.options.disabled) {
            classes.push('cursor-not-allowed', 'opacity-50');
        }

        // 自訂類名
        if (this.options.className) {
            classes.push(this.options.className);
        }

        return classes.join(' ');
    }

    getButtonContent() {
        let content = '';

        // 載入狀態
        if (this.options.loading) {
            content += '<i class="fas fa-spinner animate-spin mr-2"></i>';
        } else if (this.options.icon && this.options.iconPosition === 'left') {
            content += `<i class="${this.options.icon} mr-2"></i>`;
        }

        // 按鈕文字
        content += this.options.text;

        // 右側圖標
        if (this.options.icon && this.options.iconPosition === 'right' && !this.options.loading) {
            content += `<i class="${this.options.icon} ml-2"></i>`;
        }

        return content;
    }

    // 更新按鈕狀態
    updateState(newOptions) {
        this.options = { ...this.options, ...newOptions };
        // 如果按鈕已渲染，需要重新渲染
    }

    // 設置載入狀態
    setLoading(loading) {
        this.updateState({ loading });
    }

    // 設置禁用狀態
    setDisabled(disabled) {
        this.updateState({ disabled });
    }

    // 靜態方法：快速創建常用按鈕
    static primary(text, onClick, options = {}) {
        return new Button({
            text,
            onClick,
            type: 'primary',
            ...options
        }).render();
    }

    static secondary(text, onClick, options = {}) {
        return new Button({
            text,
            onClick,
            type: 'secondary',
            ...options
        }).render();
    }

    static danger(text, onClick, options = {}) {
        return new Button({
            text,
            onClick,
            type: 'danger',
            ...options
        }).render();
    }

    static success(text, onClick, options = {}) {
        return new Button({
            text,
            onClick,
            type: 'success',
            ...options
        }).render();
    }

    static icon(icon, onClick, options = {}) {
        return new Button({
            text: '',
            icon,
            onClick,
            size: 'sm',
            type: 'ghost',
            ...options
        }).render();
    }
}

// 導出組件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Button;
}
