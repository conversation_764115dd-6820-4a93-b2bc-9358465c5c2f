/* 專案代辦事項管理 V4.3 - 設計系統 */

:root {
  /* 色彩系統 */
  --color-primary: #3B82F6;
  --color-primary-dark: #1D4ED8;
  --color-primary-light: #93C5FD;
  --color-primary-lighter: #DBEAFE;
  
  --color-success: #10B981;
  --color-success-dark: #047857;
  --color-success-light: #6EE7B7;
  --color-success-lighter: #D1FAE5;
  
  --color-warning: #F59E0B;
  --color-warning-dark: #D97706;
  --color-warning-light: #FCD34D;
  --color-warning-lighter: #FEF3C7;
  
  --color-danger: #EF4444;
  --color-danger-dark: #DC2626;
  --color-danger-light: #F87171;
  --color-danger-lighter: #FEE2E2;
  
  --color-info: #06B6D4;
  --color-info-dark: #0891B2;
  --color-info-light: #67E8F9;
  --color-info-lighter: #CFFAFE;
  
  /* 中性色系 */
  --color-gray-50: #F9FAFB;
  --color-gray-100: #F3F4F6;
  --color-gray-200: #E5E7EB;
  --color-gray-300: #D1D5DB;
  --color-gray-400: #9CA3AF;
  --color-gray-500: #6B7280;
  --color-gray-600: #4B5563;
  --color-gray-700: #374151;
  --color-gray-800: #1F2937;
  --color-gray-900: #111827;
  
  /* 間距系統 */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-2xl: 3rem;      /* 48px */
  --space-3xl: 4rem;      /* 64px */
  
  /* 字體系統 */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  
  /* 行高系統 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* 圓角系統 */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-full: 9999px;
  
  /* 陰影系統 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 動畫系統 */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;
  --transition-bounce: 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Z-index系統 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* 基礎重置 */
* {
  box-sizing: border-box;
}

/* 工具類 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 動畫類 */
.animate-fade-in {
  animation: fadeIn var(--transition-normal);
}

.animate-slide-in {
  animation: slideIn var(--transition-normal);
}

.animate-bounce-in {
  animation: bounceIn var(--transition-bounce);
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 動畫定義 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 響應式斷點 */
@media (max-width: 767px) {
  :root {
    --space-md: 0.75rem;
    --space-lg: 1rem;
    --space-xl: 1.5rem;
  }
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
  :root {
    --color-gray-500: #000000;
    --color-gray-600: #000000;
  }
}

/* 減少動畫偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 深色模式準備 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-gray-50: #1F2937;
    --color-gray-100: #374151;
    --color-gray-200: #4B5563;
    --color-gray-300: #6B7280;
    --color-gray-400: #9CA3AF;
    --color-gray-500: #D1D5DB;
    --color-gray-600: #E5E7EB;
    --color-gray-700: #F3F4F6;
    --color-gray-800: #F9FAFB;
    --color-gray-900: #FFFFFF;
  }
}
