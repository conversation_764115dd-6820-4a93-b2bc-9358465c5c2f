# 專案代辦事項管理 - V4 開發步驟 (最終版)

## 總覽

本指南旨在將 V4 的功能規劃轉化為一個採用 Tailwind CSS 和分頁式介面的現代化 Web 應用。開發流程將確保結構清晰、樣式統一且功能穩定。

---

## 階段一：HTML 結構與 Tailwind CSS 設定

**目標**：建立符合分頁佈局的 HTML 骨架，並引入 Tailwind CSS。

1.  **檔案結構**：
    - `index.html`
    - `app.js`
    - (注意：不再需要 `style.css` 檔案)

2.  **`index.html` 設定**：
    - 在 `<head>` 中，引入 Tailwind CSS 的 CDN 腳本：
      ```html
      <script src="https://cdn.tailwindcss.com"></script>
      ```
    - **主體佈局**：使用 Flexbox (`<body class="flex h-screen bg-gray-100">`) 將頁面分為左側的 AI 助理 (`<aside>`) 和右側的主內容區 (`<div class="flex-1 flex flex-col">`)。
    - **AI 助理 (`<aside>`)**：使用 Tailwind class 設定其寬度、背景色、邊框，並使其內容垂直排列。
    - **主內容區 (`<div>`)**：
        - **分頁導航 (`<nav>`)**：包含「專案看板」和「設定」兩個按鈕。使用 Tailwind class 設計按鈕樣式，並為活動分頁提供不同的視覺效果（例如，更深的底色或邊框）。
        - **分頁內容容器 (`<main>`)**：包含兩個 `<div>`，一個用於看板 (`#board-tab-content`)，另一個用於設定 (`#settings-tab-content`)。使用 `hidden` class 來控制非活動分頁的顯示。
    - **專案看板 (`#board-tab-content`)**：
        - 頂部區域：包含專案選擇的下拉選單 (`<select>`) 和新增專案的表單，使用 Flexbox 佈局。
        - 進度條區域：一個 `<div>` 包含 `<progress>` 元素。
        - 任務列表區域：兩個 `<div>`，一個用於「待辦任務」，另一個用於「已完成任務」。
    - **設定頁 (`#settings-tab-content`)**：
        - 使用 Flexbox 或 Grid 佈局，將「API 金鑰設定」和「資料管理」顯示為兩個獨立的卡片 (`<div>`)。

---

## 階段二：JavaScript 核心邏輯 (The Brain)

**目標**：實現應用程式的狀態管理、核心功能和 DOM 更新邏輯。

1.  **狀態管理 (`State Management`)**：
    - 建立一個全域 `state` 物件，其結構與 `功能規劃_V4.md` 中定義的藍圖一致。
    - 建立 `saveState()` 和 `loadState()` 函式與 `localStorage` 互動。

2.  **渲染引擎 (`Render Engine`)**：
    - 建立一個主 `render()` 函式，作為所有 UI 更新的入口點。
    - `render()` 內部會根據 `state` 的資料，依序呼叫所有子渲染函式：
        - `renderTabs()`: 根據 `state.activeTab` 的值，為分頁按鈕添加活動樣式，並切換內容 `<div>` 的 `hidden` class。
        - `renderAIChat()`: 根據 `state.apiKey` 是否存在，鎖定或解鎖 AI 聊天視窗。
        - `renderBoard()`: 如果當前分頁是 'board'，則渲染整個看板內容，包括專案選擇器、進度條和任務卡片。
        - `renderSettings()`: 如果當前分頁是 'settings'，則更新設定頁中的 API 金鑰輸入框。

3.  **DOM 元素創建 (`Element Factory`)**：
    - **`createTaskCard(task)`**：這是最重要的渲染輔助函式。它會回傳一個使用 Tailwind class 構建的任務卡片 `<div>` 的 DOM 元素。卡片內部會包含所有必要的子元素和 `data-` 屬性，以便事件監聽。

4.  **核心功能 (`Core Functions`)**：
    - 建立 `addProject()`, `addTask()`, `addSubtask()`, `toggleTaskCompletion()`, `toggleSubtaskCompletion()`, `toggleTaskExpansion()` 等函式。這些函式**只修改 `state`**，然後呼叫 `render()` 來更新 UI。

---

## 階段三：事件監聽與整合 (The Nerves)

**目標**：將使用者的操作與核心邏輯連接起來，並整合外部服務。

1.  **分頁切換**：監聽分頁按鈕的點擊，更新 `state.activeTab`，然後呼叫 `render()`。
2.  **專案選擇與新增**：監聽專案下拉選單的 `change` 事件和新增專案表單的 `submit` 事件，更新 `state` 並重新渲染。
3.  **任務互動 (事件委派)**：在任務列表的父容器上設定事件監聽器。根據被點擊元素的 `data-action` 屬性（例如 `<button data-action="toggle-expand">`）來判斷使用者意圖，然後呼叫對應的核心功能函式並重新渲染。
4.  **設定頁互動**：監聽「儲存 API 金鑰」按鈕的點擊，以及匯入/匯出按鈕的事件。
5.  **AI 助理整合 (真實 API - 智慧規劃)**：
    - **API 呼叫**：建立 `async function callGeminiAPI(prompt)`，使用 `fetch` 與 Google AI 服務溝通。
    - **提示工程 (Prompt Engineering)**：設計一個能讓 AI 在「快速新增」和「完整規劃」兩種模式下運作的提示，並要求其回傳特定結構的 JSON。
    - **回應處理與動作分派**：建立 `executeAIAction(command)` 函式，使用 `switch` 語句來分派 `addTask` 或 `addProjectPlan` 等不同的動作。
        - `case 'addProjectPlan'`: 直接將 `command.payload` (一個完整的專案物件) `push` 到 `state.projects` 陣列中，並可選擇性地將 `state.currentProjectId` 切換到這個新專案。
    - **UI 更新**：在 `executeAIAction` 執行完畢後，呼叫主 `render()` 函式，讓整個 UI 自動更新。
