# 專案代辦事項管理 - V4 功能規劃 (最終版)

## 1. 專案願景 (Project Vision)

打造一個採用現代化介面 (Modern UI) 的智慧專案管理工具。透過引入 Tailwind CSS，我們將建構一個乾淨、響應式且以卡片為基礎的佈局。功能上，透過分頁 (Tabs) 將工作區與設定區明確分離，提升使用者專注度。AI 助理和完整的資料匯入/匯出功能，共同構成一個強大且使用者友好的個人生產力平台。

---

## 2. UI/UX 核心概念

- **佈局**：應用程式將採用分頁式主介面。
    - **Tab 1: 專案看板 (Board)**：這是使用者的主要工作區，包含專案選擇、進度顯示和任務卡片列表。
    - **Tab 2: 設定 (Settings)**：此頁面專門用於配置應用程式，包含 Gemini API 金鑰設定和資料匯入/匯出功能。
- **樣式**：全面採用 **Tailwind CSS** 進行樣式設計，確保視覺一致性、可維護性和現代感。
- **任務呈現**：每個主任務都將是一個獨立的 **卡片 (Card)**。卡片內部會清晰地劃分出標題、進度、日期和子任務區域。

---

## 3. 核心功能詳解 (Detailed Feature Breakdown)

### 3.1 專案與任務管理
- **專案管理**：
    - **建立**：使用者可以在「專案看板」頁透過一個專門的表單新增專案。
    - **選擇**：使用者可以透過一個清晰的下拉式選單 (`<select>`) 來快速切換當前要操作的專案。
- **層級結構**：嚴格支援「主任務 -> 子任務」的二級結構。
- **任務新增**：
    - 可在當前專案下新增主任務。
    - 可在指定的主任務卡片內新增子任務。
- **狀態管理**：
    - **待辦 (To-Do)** 與 **已完成 (Completed)**：使用者可透過勾選任務卡片上的核取方塊，快速切換任務的完成狀態。
    - **列表分離**：在「專案看板」頁，待辦任務卡片和已完成任務卡片會被分別渲染到兩個不同的視覺區塊中。
    - **結構完整性**：子任務永遠保持在其父任務的卡片內部，不會因狀態改變而脫離。
- **取消完成**：使用者可以將「已完成」的任務取消勾選，使其狀態還原為「待辦」。

### 3.2 任務屬性與視覺化
- **創建與完成日期**：
    - 所有任務和子任務在建立時，自動記錄 `creationDate`。
    - 當任務被標記為「完成」時，記錄 `completionDate`；被「取消完成」時，此日期將被清空。
    - 這兩個日期都會清晰地顯示在任務卡片上。
- **展開與收折**：主任務卡片上將提供一個可點擊的區域（例如，標題或一個專門的圖示），用於展開或收折其下的子任務列表。
- **進度統計**：
    - **子任務進度**：對於有子任務的主任務，其卡片上會顯示子任務的完成比例統計（例如 `(3/5)`）。
    - **專案總進度**：在「專案看板」頁的頂部，一個全域的進度條會顯示整個專案所有主任務的完成百分比。

### 3.3 AI 智慧助理
- **互動介面**：一個固定在畫面左側的聊天視窗，可隨時呼叫。
- **API 金鑰管理**：
    - AI 功能需要使用者在「設定」分頁中提供自己的 Gemini API 金鑰。
    - 當金鑰未設定時，聊天視窗應呈現為不可操作的「鎖定」狀態，並引導使用者前往設定。
    - 設定頁面需提供清晰的說明，指引使用者如何獲取金鑰。
- **智慧專案規劃**：AI 能夠理解複雜指令，例如「幫我規劃一個『新產品發布』專案」，並一次性生成一個包含多個邏輯相關的任務與子任務的完整專案計畫。
- **快速任務創建**：AI 同時支援簡單指令，例如「在目前專案新增任務：市場調研」，以快速新增單一任務。

### 3.4 資料可攜性
- **匯出**：在「設定」分頁，使用者可以點擊按鈕，將包含所有專案、任務及 API 金鑰的完整應用程式狀態，匯出成一個 `JSON` 檔案。
- **匯入**：在「設定」分頁，使用者可以選擇之前匯出的 `JSON` 檔案，將其資料完整還原至應用程式中。

---

## 4. 資料結構藍圖 (Data Schema)

整個應用程式的狀態將儲存在一個根物件中，其結構如下：

```json
{
  "apiKey": "g-xxxxxxxxxx",
  "currentProjectId": "proj-1660000000000",
  "activeTab": "board", // 新增：用於追蹤當前分頁
  "projects": [
    {
      "id": "proj-1660000000000",
      "name": "網站開發專案",
      "tasks": [
        {
          "id": "task-1660000100000",
          "text": "規劃與設計",
          "completed": false,
          "expanded": true,
          "creationDate": "2025-08-06T10:00:00.000Z",
          "completionDate": null,
          "subtasks": [ ... ]
        }
      ]
    }
  ]
}
```
