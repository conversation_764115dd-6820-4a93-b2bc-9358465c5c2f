/**
 * SearchBox Component - V4.4
 * 智慧搜尋框組件，支援即時搜尋和建議
 */

class SearchBox {
    constructor(container, searchManager, options = {}) {
        this.container = container;
        this.searchManager = searchManager;
        this.options = {
            placeholder: options.placeholder || '搜尋專案、任務...',
            showSuggestions: options.showSuggestions !== false,
            showHistory: options.showHistory !== false,
            maxSuggestions: options.maxSuggestions || 5,
            minQueryLength: options.minQueryLength || 1,
            debounceDelay: options.debounceDelay || 300,
            ...options
        };

        this.input = null;
        this.dropdown = null;
        this.debounceTimer = null;
        this.isOpen = false;
        this.currentQuery = '';
        this.selectedIndex = -1;
        this.results = [];

        this.init();
    }

    init() {
        this.createElements();
        this.bindEvents();
    }

    createElements() {
        // 搜尋輸入框
        this.input = this.container.querySelector('#global-search');
        if (!this.input) {
            console.error('Search input not found');
            return;
        }

        // 搜尋結果下拉框
        this.dropdown = this.container.querySelector('#search-results');
        if (!this.dropdown) {
            console.error('Search dropdown not found');
            return;
        }

        // 設置初始狀態
        this.input.placeholder = this.options.placeholder;
    }

    bindEvents() {
        if (!this.input || !this.dropdown) return;

        // 輸入事件
        this.input.addEventListener('input', (e) => {
            this.handleInput(e.target.value);
        });

        // 焦點事件
        this.input.addEventListener('focus', () => {
            this.handleFocus();
        });

        // 失去焦點事件
        this.input.addEventListener('blur', (e) => {
            // 延遲隱藏，允許點擊下拉選項
            setTimeout(() => {
                this.hideDropdown();
            }, 200);
        });

        // 鍵盤事件
        this.input.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        // 下拉框點擊事件
        this.dropdown.addEventListener('click', (e) => {
            this.handleDropdownClick(e);
        });

        // 全域點擊事件（關閉下拉框）
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.hideDropdown();
            }
        });

        // 快捷鍵支援
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                this.focus();
            }
        });
    }

    handleInput(value) {
        this.currentQuery = value.trim();
        
        clearTimeout(this.debounceTimer);
        
        if (this.currentQuery.length === 0) {
            this.showHistory();
            return;
        }

        if (this.currentQuery.length < this.options.minQueryLength) {
            this.hideDropdown();
            return;
        }

        // 防抖搜尋
        this.debounceTimer = setTimeout(() => {
            this.performSearch();
        }, this.options.debounceDelay);
    }

    handleFocus() {
        if (this.currentQuery.length === 0) {
            this.showHistory();
        } else if (this.results.length > 0) {
            this.showDropdown();
        }
    }

    handleKeydown(e) {
        if (!this.isOpen) return;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectNext();
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.selectPrevious();
                break;
            case 'Enter':
                e.preventDefault();
                this.selectCurrent();
                break;
            case 'Escape':
                e.preventDefault();
                this.hideDropdown();
                this.input.blur();
                break;
        }
    }

    handleDropdownClick(e) {
        const item = e.target.closest('.search-result-item');
        if (!item) return;

        const action = item.dataset.action;
        const value = item.dataset.value;

        switch (action) {
            case 'search':
                this.selectResult(value);
                break;
            case 'history':
                this.input.value = value;
                this.currentQuery = value;
                this.performSearch();
                break;
            case 'suggestion':
                this.input.value = value;
                this.currentQuery = value;
                this.performSearch();
                break;
            case 'clear-history':
                this.clearHistory();
                break;
        }
    }

    performSearch() {
        if (!this.currentQuery) return;

        // 執行搜尋
        this.searchManager.debouncedSearch(this.currentQuery, (results) => {
            this.results = results;
            this.showResults(results);
        });

        // 同時獲取建議
        if (this.options.showSuggestions) {
            const suggestions = this.searchManager.getSuggestions(this.currentQuery, this.options.maxSuggestions);
            this.showSuggestions(suggestions);
        }
    }

    showResults(results) {
        if (results.length === 0) {
            this.showNoResults();
            return;
        }

        const html = this.renderResults(results);
        this.dropdown.innerHTML = html;
        this.showDropdown();
        this.selectedIndex = -1;
    }

    showHistory() {
        if (!this.options.showHistory) return;

        const history = this.searchManager.getSearchHistory();
        if (history.length === 0) return;

        const html = this.renderHistory(history);
        this.dropdown.innerHTML = html;
        this.showDropdown();
        this.selectedIndex = -1;
    }

    showSuggestions(suggestions) {
        if (suggestions.length === 0) return;

        const html = this.renderSuggestions(suggestions);
        this.dropdown.innerHTML = html;
        this.showDropdown();
        this.selectedIndex = -1;
    }

    showNoResults() {
        const html = `
            <div class="p-4 text-center text-gray-500">
                <i class="fas fa-search text-2xl mb-2"></i>
                <p>沒有找到相關結果</p>
                <p class="text-sm">嘗試使用不同的關鍵字</p>
            </div>
        `;
        this.dropdown.innerHTML = html;
        this.showDropdown();
    }

    renderResults(results) {
        const items = results.slice(0, 10).map((result, index) => {
            const icon = this.getResultIcon(result.type);
            const badge = this.getResultBadge(result.type);
            const highlights = result.highlights || [];
            
            return `
                <div class="search-result-item p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 ${index === this.selectedIndex ? 'bg-blue-50' : ''}" 
                     data-action="search" data-value="${result.id}" data-type="${result.type}">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <i class="${icon} text-gray-400"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2">
                                <h4 class="text-sm font-medium text-gray-900 truncate">
                                    ${this.highlightText(result.title, this.currentQuery)}
                                </h4>
                                <span class="${badge.class}">${badge.text}</span>
                            </div>
                            ${result.projectName ? `<p class="text-xs text-gray-500">專案：${result.projectName}</p>` : ''}
                            ${result.content ? `<p class="text-xs text-gray-600 mt-1 truncate">${this.highlightText(result.content, this.currentQuery)}</p>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="search-results">
                <div class="p-2 border-b border-gray-200 bg-gray-50">
                    <span class="text-xs font-medium text-gray-600">搜尋結果 (${results.length})</span>
                </div>
                ${items}
            </div>
        `;
    }

    renderHistory(history) {
        const items = history.slice(0, 5).map((item, index) => `
            <div class="search-result-item p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 ${index === this.selectedIndex ? 'bg-blue-50' : ''}" 
                 data-action="history" data-value="${item}">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-history text-gray-400"></i>
                    <span class="text-sm text-gray-700">${item}</span>
                </div>
            </div>
        `).join('');

        return `
            <div class="search-history">
                <div class="p-2 border-b border-gray-200 bg-gray-50 flex items-center justify-between">
                    <span class="text-xs font-medium text-gray-600">搜尋歷史</span>
                    <button class="text-xs text-blue-600 hover:text-blue-800" data-action="clear-history">清除</button>
                </div>
                ${items}
            </div>
        `;
    }

    renderSuggestions(suggestions) {
        const items = suggestions.map((item, index) => `
            <div class="search-result-item p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 ${index === this.selectedIndex ? 'bg-blue-50' : ''}" 
                 data-action="suggestion" data-value="${item}">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-lightbulb text-gray-400"></i>
                    <span class="text-sm text-gray-700">${item}</span>
                </div>
            </div>
        `).join('');

        return `
            <div class="search-suggestions">
                <div class="p-2 border-b border-gray-200 bg-gray-50">
                    <span class="text-xs font-medium text-gray-600">建議</span>
                </div>
                ${items}
            </div>
        `;
    }

    getResultIcon(type) {
        const icons = {
            project: 'fas fa-folder',
            task: 'fas fa-tasks',
            subtask: 'fas fa-check-circle'
        };
        return icons[type] || 'fas fa-file';
    }

    getResultBadge(type) {
        const badges = {
            project: { class: 'px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full', text: '專案' },
            task: { class: 'px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full', text: '任務' },
            subtask: { class: 'px-2 py-0.5 text-xs bg-yellow-100 text-yellow-800 rounded-full', text: '子任務' }
        };
        return badges[type] || { class: 'px-2 py-0.5 text-xs bg-gray-100 text-gray-800 rounded-full', text: '項目' };
    }

    highlightText(text, query) {
        if (!query) return text;
        const regex = new RegExp(`(${this.escapeRegex(query)})`, 'gi');
        return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    selectNext() {
        const items = this.dropdown.querySelectorAll('.search-result-item');
        if (items.length === 0) return;

        this.selectedIndex = Math.min(this.selectedIndex + 1, items.length - 1);
        this.updateSelection();
    }

    selectPrevious() {
        const items = this.dropdown.querySelectorAll('.search-result-item');
        if (items.length === 0) return;

        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
        this.updateSelection();
    }

    selectCurrent() {
        const items = this.dropdown.querySelectorAll('.search-result-item');
        if (this.selectedIndex >= 0 && this.selectedIndex < items.length) {
            items[this.selectedIndex].click();
        }
    }

    updateSelection() {
        const items = this.dropdown.querySelectorAll('.search-result-item');
        items.forEach((item, index) => {
            item.classList.toggle('bg-blue-50', index === this.selectedIndex);
        });
    }

    selectResult(resultId) {
        const result = this.results.find(r => r.id === resultId);
        if (!result) return;

        // 觸發選擇事件
        const event = new CustomEvent('searchSelect', {
            detail: { result, query: this.currentQuery }
        });
        document.dispatchEvent(event);

        this.hideDropdown();
        this.input.blur();
    }

    clearHistory() {
        this.searchManager.clearSearchHistory();
        this.hideDropdown();
        Toast.info('搜尋歷史已清除');
    }

    showDropdown() {
        this.dropdown.classList.remove('hidden');
        this.isOpen = true;
    }

    hideDropdown() {
        this.dropdown.classList.add('hidden');
        this.isOpen = false;
        this.selectedIndex = -1;
    }

    focus() {
        this.input.focus();
    }

    clear() {
        this.input.value = '';
        this.currentQuery = '';
        this.hideDropdown();
    }

    setValue(value) {
        this.input.value = value;
        this.currentQuery = value;
    }

    getValue() {
        return this.input.value;
    }
}

// 導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchBox;
} else {
    window.SearchBox = SearchBox;
}
