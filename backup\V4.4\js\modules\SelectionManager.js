/**
 * SelectionManager - V4.3
 * 多選和批量操作管理器
 */

class SelectionManager {
    constructor(stateManager) {
        this.stateManager = stateManager;
        this.selectedItems = new Set();
        this.selectionMode = false;
        this.selectionType = 'task'; // task, project, subtask
        
        // 監聽狀態變化
        this.stateManager.subscribe((newState, oldState) => {
            // 如果專案變更，清除選擇
            if (newState.currentProjectId !== oldState.currentProjectId) {
                this.clearSelection();
            }
        });
    }

    // 進入選擇模式
    enterSelectionMode(type = 'task') {
        this.selectionMode = true;
        this.selectionType = type;
        this.selectedItems.clear();
        
        this.stateManager.setState({
            ui: {
                ...this.stateManager.getState().ui,
                bulkOperationMode: true,
                selectedTasks: new Set()
            }
        });

        this.updateUI();
        this.notifySelectionChange();
    }

    // 退出選擇模式
    exitSelectionMode() {
        this.selectionMode = false;
        this.selectedItems.clear();
        
        this.stateManager.setState({
            ui: {
                ...this.stateManager.getState().ui,
                bulkOperationMode: false,
                selectedTasks: new Set()
            }
        });

        this.updateUI();
        this.notifySelectionChange();
    }

    // 切換選擇模式
    toggleSelectionMode(type = 'task') {
        if (this.selectionMode) {
            this.exitSelectionMode();
        } else {
            this.enterSelectionMode(type);
        }
    }

    // 選擇項目
    selectItem(itemId) {
        if (!this.selectionMode) {
            this.enterSelectionMode();
        }

        this.selectedItems.add(itemId);
        this.updateStateSelection();
        this.updateUI();
        this.notifySelectionChange();
    }

    // 取消選擇項目
    deselectItem(itemId) {
        this.selectedItems.delete(itemId);
        this.updateStateSelection();
        this.updateUI();
        this.notifySelectionChange();
    }

    // 切換項目選擇狀態
    toggleItem(itemId) {
        if (this.selectedItems.has(itemId)) {
            this.deselectItem(itemId);
        } else {
            this.selectItem(itemId);
        }
    }

    // 全選
    selectAll() {
        const items = this.getAllSelectableItems();
        items.forEach(itemId => {
            this.selectedItems.add(itemId);
        });
        
        this.updateStateSelection();
        this.updateUI();
        this.notifySelectionChange();
    }

    // 清除選擇
    clearSelection() {
        this.selectedItems.clear();
        this.updateStateSelection();
        this.updateUI();
        this.notifySelectionChange();
    }

    // 反選
    invertSelection() {
        const allItems = this.getAllSelectableItems();
        const newSelection = new Set();
        
        allItems.forEach(itemId => {
            if (!this.selectedItems.has(itemId)) {
                newSelection.add(itemId);
            }
        });
        
        this.selectedItems = newSelection;
        this.updateStateSelection();
        this.updateUI();
        this.notifySelectionChange();
    }

    // 獲取所有可選擇的項目
    getAllSelectableItems() {
        const state = this.stateManager.getState();
        const items = [];

        if (this.selectionType === 'project') {
            return state.projects.map(p => p.id);
        }

        if (this.selectionType === 'task') {
            const currentProject = state.projects.find(p => p.id === state.currentProjectId);
            if (currentProject) {
                return currentProject.tasks.map(t => t.id);
            }
        }

        return items;
    }

    // 更新狀態中的選擇
    updateStateSelection() {
        this.stateManager.setState({
            ui: {
                ...this.stateManager.getState().ui,
                selectedTasks: new Set(this.selectedItems)
            }
        }, { silent: true });
    }

    // 獲取選中的項目
    getSelectedItems() {
        return Array.from(this.selectedItems);
    }

    // 獲取選中項目數量
    getSelectionCount() {
        return this.selectedItems.size;
    }

    // 檢查項目是否被選中
    isSelected(itemId) {
        return this.selectedItems.has(itemId);
    }

    // 檢查是否全選
    isAllSelected() {
        const allItems = this.getAllSelectableItems();
        return allItems.length > 0 && allItems.every(itemId => this.selectedItems.has(itemId));
    }

    // 檢查是否部分選中
    isPartiallySelected() {
        const selectedCount = this.getSelectionCount();
        const totalCount = this.getAllSelectableItems().length;
        return selectedCount > 0 && selectedCount < totalCount;
    }

    // 更新UI
    updateUI() {
        const toolbar = document.getElementById('bulk-operations-toolbar');
        const selectionCount = document.getElementById('selection-count');
        const selectAllBtn = document.getElementById('select-all-tasks');
        
        if (toolbar) {
            toolbar.classList.toggle('hidden', !this.selectionMode);
        }

        if (selectionCount) {
            selectionCount.textContent = this.getSelectionCount();
        }

        if (selectAllBtn) {
            if (this.isAllSelected()) {
                selectAllBtn.textContent = '取消全選';
            } else {
                selectAllBtn.textContent = '全選';
            }
        }

        // 更新任務卡片的選擇狀態
        this.updateTaskCardSelection();
    }

    // 更新任務卡片的選擇狀態
    updateTaskCardSelection() {
        const taskCards = document.querySelectorAll('.task-card');

        taskCards.forEach(card => {
            const taskId = card.dataset.taskId;
            const checkboxContainer = card.querySelector('.task-selection-container');
            const checkbox = card.querySelector('.task-selection-checkbox');
            const completeCheckbox = card.querySelector('input[data-action="toggle-complete"]');
            const completeClickArea = card.querySelector('div[data-action="toggle-complete"]');

            if (this.selectionMode) {
                // 顯示選擇框
                if (!checkboxContainer) {
                    this.addSelectionCheckbox(card, taskId);
                }
                card.classList.add('selection-mode');

                // 禁用原本的完成點擊區域（如果存在），避免混淆
                if (completeClickArea) {
                    completeClickArea.style.opacity = '0.3';
                    completeClickArea.style.cursor = 'not-allowed';
                    completeClickArea.style.pointerEvents = 'none';
                    completeClickArea.title = '批量模式下請使用左側的選擇框';
                }
                if (completeCheckbox) {
                    completeCheckbox.disabled = true;
                }
            } else {
                // 隱藏選擇框容器
                if (checkboxContainer) {
                    checkboxContainer.remove();
                }
                card.classList.remove('selection-mode', 'selected');

                // 恢復原本的完成點擊區域（如果存在）
                if (completeClickArea) {
                    completeClickArea.style.opacity = '';
                    completeClickArea.style.cursor = '';
                    completeClickArea.style.pointerEvents = '';
                    completeClickArea.title = '';
                }
                if (completeCheckbox) {
                    completeCheckbox.disabled = false;
                }
            }

            // 更新選中狀態
            if (this.isSelected(taskId)) {
                card.classList.add('selected');
                if (checkbox) {
                    checkbox.checked = true;
                }
            } else {
                card.classList.remove('selected');
                if (checkbox) {
                    checkbox.checked = false;
                }
            }
        });
    }

    // 添加選擇框到任務卡片
    addSelectionCheckbox(card, taskId) {
        const checkboxContainer = document.createElement('div');
        checkboxContainer.className = 'task-selection-container flex items-center mr-3';

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'task-selection-checkbox h-5 w-5 text-blue-600 border-2 border-blue-500 rounded focus:ring-2 focus:ring-blue-500';
        checkbox.checked = this.isSelected(taskId);
        checkbox.title = '選擇此任務進行批量操作';

        // 添加視覺標識
        const label = document.createElement('span');
        label.className = 'text-xs text-blue-600 font-medium ml-1';
        label.textContent = '選擇';

        checkbox.addEventListener('change', (e) => {
            e.stopPropagation();
            this.toggleItem(taskId);
        });

        checkboxContainer.appendChild(checkbox);
        checkboxContainer.appendChild(label);

        // 插入到任務卡片的開始位置
        const firstChild = card.querySelector('.flex.items-center');
        if (firstChild) {
            firstChild.insertBefore(checkboxContainer, firstChild.firstChild);
        }
    }

    // 通知選擇變化
    notifySelectionChange() {
        const event = new CustomEvent('selectionChange', {
            detail: {
                selectedItems: this.getSelectedItems(),
                selectionCount: this.getSelectionCount(),
                selectionMode: this.selectionMode,
                selectionType: this.selectionType
            }
        });
        
        document.dispatchEvent(event);
    }

    // 綁定事件監聽器
    bindEvents() {
        // 全選按鈕
        const selectAllBtn = document.getElementById('select-all-tasks');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => {
                if (this.isAllSelected()) {
                    this.clearSelection();
                } else {
                    this.selectAll();
                }
            });
        }

        // 清除選擇按鈕
        const clearSelectionBtn = document.getElementById('clear-selection');
        if (clearSelectionBtn) {
            clearSelectionBtn.addEventListener('click', () => {
                this.clearSelection();
            });
        }

        // 退出批量模式按鈕
        const exitBulkModeBtn = document.getElementById('exit-bulk-mode');
        if (exitBulkModeBtn) {
            exitBulkModeBtn.addEventListener('click', () => {
                this.exitSelectionMode();
            });
        }

        // 任務卡片點擊事件（在選擇模式下）
        document.addEventListener('click', (e) => {
            if (!this.selectionMode) return;
            
            const taskCard = e.target.closest('.task-card');
            if (taskCard && !e.target.closest('input, button, a')) {
                const taskId = taskCard.dataset.taskId;
                this.toggleItem(taskId);
            }
        });

        // 鍵盤快捷鍵
        document.addEventListener('keydown', (e) => {
            if (this.selectionMode) {
                if (e.key === 'Escape') {
                    this.exitSelectionMode();
                } else if (e.ctrlKey && e.key === 'a') {
                    e.preventDefault();
                    this.selectAll();
                }
            }
        });
    }

    // 獲取選中項目的詳細資訊
    getSelectedItemsDetails() {
        const state = this.stateManager.getState();
        const details = [];

        if (this.selectionType === 'task') {
            const currentProject = state.projects.find(p => p.id === state.currentProjectId);
            if (currentProject) {
                this.selectedItems.forEach(taskId => {
                    const task = currentProject.tasks.find(t => t.id === taskId);
                    if (task) {
                        details.push({
                            type: 'task',
                            id: taskId,
                            projectId: currentProject.id,
                            data: task
                        });
                    }
                });
            }
        }

        return details;
    }
}

// 導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SelectionManager;
} else {
    window.SelectionManager = SelectionManager;
}
