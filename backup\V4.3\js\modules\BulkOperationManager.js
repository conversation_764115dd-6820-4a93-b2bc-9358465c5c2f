/**
 * BulkOperationManager - V4.3
 * 批量操作管理器，處理多個項目的批量操作
 */

class BulkOperationManager {
    constructor(stateManager, selectionManager) {
        this.stateManager = stateManager;
        this.selectionManager = selectionManager;
        this.operationInProgress = false;
        
        this.bindEvents();
    }

    // 批量標記完成
    async bulkComplete(taskIds = null) {
        const ids = taskIds || this.selectionManager.getSelectedItems();
        if (ids.length === 0) {
            Toast.warning('請先選擇要操作的任務');
            return;
        }

        const confirmMessage = `確定要將 ${ids.length} 個任務標記為完成嗎？`;
        
        return new Promise((resolve) => {
            Modal.confirm('批量完成任務', confirmMessage, async () => {
                const loadingToast = Toast.loading(`正在處理 ${ids.length} 個任務...`);
                
                try {
                    const results = await this.processTasksInBatch(ids, (task) => {
                        // 對於有子任務的任務，批量完成所有子任務，主任務狀態會自動更新
                        if (task.subtasks && task.subtasks.length > 0) {
                            let hasChanges = false;
                            task.subtasks.forEach(subtask => {
                                if (!subtask.completed) {
                                    subtask.completed = true;
                                    subtask.completionDate = new Date().toISOString();
                                    hasChanges = true;
                                }
                            });

                            // 檢查並更新主任務狀態
                            const allSubtasksCompleted = task.subtasks.every(st => st.completed);
                            if (allSubtasksCompleted) {
                                task.completed = true;
                                task.completionDate = new Date().toISOString();
                            }

                            return hasChanges ? { success: true, action: 'completed' } : { success: false, reason: 'already_completed' };
                        } else {
                            // 對於沒有子任務的任務，直接切換完成狀態
                            if (!task.completed) {
                                task.completed = true;
                                task.completionDate = new Date().toISOString();
                                return { success: true, action: 'completed' };
                            }
                            return { success: false, reason: 'already_completed' };
                        }
                    });

                    Toast.remove(loadingToast);
                    this.showBatchResults(results, '完成');
                    this.selectionManager.clearSelection();

                    // 觸發UI重新渲染
                    document.dispatchEvent(new CustomEvent('refreshTaskBoard'));

                    resolve(results);
                    
                } catch (error) {
                    Toast.remove(loadingToast);
                    Toast.error('批量操作失敗：' + error.message);
                    resolve({ success: false, error });
                }
            });
        });
    }

    // 批量標記未完成
    async bulkUncomplete(taskIds = null) {
        const ids = taskIds || this.selectionManager.getSelectedItems();
        if (ids.length === 0) {
            Toast.warning('請先選擇要操作的任務');
            return;
        }

        const confirmMessage = `確定要將 ${ids.length} 個任務標記為未完成嗎？`;
        
        return new Promise((resolve) => {
            Modal.confirm('批量取消完成', confirmMessage, async () => {
                const loadingToast = Toast.loading(`正在處理 ${ids.length} 個任務...`);
                
                try {
                    const results = await this.processTasksInBatch(ids, (task) => {
                        // 對於有子任務的任務，批量取消完成所有子任務，主任務狀態會自動更新
                        if (task.subtasks && task.subtasks.length > 0) {
                            let hasChanges = false;
                            task.subtasks.forEach(subtask => {
                                if (subtask.completed) {
                                    subtask.completed = false;
                                    subtask.completionDate = null;
                                    hasChanges = true;
                                }
                            });

                            // 檢查並更新主任務狀態
                            const allSubtasksCompleted = task.subtasks.every(st => st.completed);
                            if (allSubtasksCompleted) {
                                task.completed = true;
                                task.completionDate = new Date().toISOString();
                            } else {
                                // 只要有任何子任務未完成，主任務就標記為未完成
                                task.completed = false;
                                task.completionDate = null;
                            }

                            return hasChanges ? { success: true, action: 'uncompleted' } : { success: false, reason: 'already_uncompleted' };
                        } else {
                            // 對於沒有子任務的任務，直接切換完成狀態
                            if (task.completed) {
                                task.completed = false;
                                task.completionDate = null;
                                return { success: true, action: 'uncompleted' };
                            }
                            return { success: false, reason: 'already_uncompleted' };
                        }
                    });

                    Toast.remove(loadingToast);
                    this.showBatchResults(results, '取消完成');
                    this.selectionManager.clearSelection();

                    // 觸發UI重新渲染
                    document.dispatchEvent(new CustomEvent('refreshTaskBoard'));

                    resolve(results);
                    
                } catch (error) {
                    Toast.remove(loadingToast);
                    Toast.error('批量操作失敗：' + error.message);
                    resolve({ success: false, error });
                }
            });
        });
    }

    // 批量移動任務
    async bulkMove(targetProjectId = null, taskIds = null) {
        const ids = taskIds || this.selectionManager.getSelectedItems();
        if (ids.length === 0) {
            Toast.warning('請先選擇要移動的任務');
            return;
        }

        const state = this.stateManager.getState();
        const projects = state.projects.filter(p => p.id !== state.currentProjectId);
        
        if (projects.length === 0) {
            Toast.warning('沒有其他專案可以移動到');
            return;
        }

        // 如果沒有指定目標專案，顯示選擇對話框
        if (!targetProjectId) {
            return this.showMoveDialog(ids, projects);
        }

        const targetProject = state.projects.find(p => p.id === targetProjectId);
        if (!targetProject) {
            Toast.error('目標專案不存在');
            return;
        }

        const confirmMessage = `確定要將 ${ids.length} 個任務移動到「${targetProject.name}」嗎？`;
        
        return new Promise((resolve) => {
            Modal.confirm('批量移動任務', confirmMessage, async () => {
                const loadingToast = Toast.loading(`正在移動 ${ids.length} 個任務...`);
                
                try {
                    const results = await this.moveTasksToProject(ids, targetProjectId);
                    
                    Toast.remove(loadingToast);
                    Toast.success(`成功移動 ${results.successCount} 個任務到「${targetProject.name}」`);
                    this.selectionManager.clearSelection();

                    // 觸發UI重新渲染
                    document.dispatchEvent(new CustomEvent('refreshTaskBoard'));

                    resolve(results);
                    
                } catch (error) {
                    Toast.remove(loadingToast);
                    Toast.error('移動任務失敗：' + error.message);
                    resolve({ success: false, error });
                }
            });
        });
    }

    // 批量刪除任務
    async bulkDelete(taskIds = null) {
        const ids = taskIds || this.selectionManager.getSelectedItems();
        if (ids.length === 0) {
            Toast.warning('請先選擇要刪除的任務');
            return;
        }

        const confirmMessage = `確定要刪除 ${ids.length} 個任務嗎？此操作無法撤銷。`;
        
        return new Promise((resolve) => {
            Modal.confirm('批量刪除任務', confirmMessage, async () => {
                const loadingToast = Toast.loading(`正在刪除 ${ids.length} 個任務...`);
                
                try {
                    const results = await this.deleteTasksFromProject(ids);
                    
                    Toast.remove(loadingToast);
                    Toast.success(`成功刪除 ${results.successCount} 個任務`);
                    this.selectionManager.clearSelection();

                    // 觸發UI重新渲染
                    document.dispatchEvent(new CustomEvent('refreshTaskBoard'));

                    resolve(results);
                    
                } catch (error) {
                    Toast.remove(loadingToast);
                    Toast.error('刪除任務失敗：' + error.message);
                    resolve({ success: false, error });
                }
            });
        });
    }

    // 批量處理任務
    async processTasksInBatch(taskIds, processor) {
        const state = this.stateManager.getState();
        const currentProject = state.projects.find(p => p.id === state.currentProjectId);
        
        if (!currentProject) {
            throw new Error('當前專案不存在');
        }

        const results = {
            total: taskIds.length,
            successCount: 0,
            failureCount: 0,
            details: []
        };

        // 處理每個任務
        for (const taskId of taskIds) {
            const task = currentProject.tasks.find(t => t.id === taskId);
            
            if (!task) {
                results.details.push({
                    taskId,
                    success: false,
                    reason: 'task_not_found'
                });
                results.failureCount++;
                continue;
            }

            try {
                const result = processor(task);
                
                if (result.success) {
                    results.successCount++;
                } else {
                    results.failureCount++;
                }
                
                results.details.push({
                    taskId,
                    taskName: task.text,
                    ...result
                });
                
            } catch (error) {
                results.details.push({
                    taskId,
                    taskName: task.text,
                    success: false,
                    error: error.message
                });
                results.failureCount++;
            }
        }

        // 更新狀態
        this.stateManager.setState({
            projects: [...state.projects]
        });

        return results;
    }

    // 移動任務到其他專案
    async moveTasksToProject(taskIds, targetProjectId) {
        const state = this.stateManager.getState();
        const currentProject = state.projects.find(p => p.id === state.currentProjectId);
        const targetProject = state.projects.find(p => p.id === targetProjectId);
        
        if (!currentProject || !targetProject) {
            throw new Error('專案不存在');
        }

        const results = {
            total: taskIds.length,
            successCount: 0,
            failureCount: 0,
            details: []
        };

        const tasksToMove = [];
        
        // 收集要移動的任務
        for (const taskId of taskIds) {
            const taskIndex = currentProject.tasks.findIndex(t => t.id === taskId);
            
            if (taskIndex !== -1) {
                const task = currentProject.tasks[taskIndex];
                tasksToMove.push({ task, index: taskIndex });
                results.successCount++;
            } else {
                results.failureCount++;
                results.details.push({
                    taskId,
                    success: false,
                    reason: 'task_not_found'
                });
            }
        }

        // 移動任務（從後往前刪除，避免索引問題）
        tasksToMove.sort((a, b) => b.index - a.index);
        
        for (const { task, index } of tasksToMove) {
            // 從當前專案移除
            currentProject.tasks.splice(index, 1);
            
            // 添加到目標專案
            targetProject.tasks.push(task);
            
            results.details.push({
                taskId: task.id,
                taskName: task.text,
                success: true,
                action: 'moved'
            });
        }

        // 更新狀態
        this.stateManager.setState({
            projects: [...state.projects]
        });

        return results;
    }

    // 從專案中刪除任務
    async deleteTasksFromProject(taskIds) {
        const state = this.stateManager.getState();
        const currentProject = state.projects.find(p => p.id === state.currentProjectId);
        
        if (!currentProject) {
            throw new Error('當前專案不存在');
        }

        const results = {
            total: taskIds.length,
            successCount: 0,
            failureCount: 0,
            details: []
        };

        // 收集要刪除的任務索引（從後往前刪除）
        const tasksToDelete = [];
        
        for (const taskId of taskIds) {
            const taskIndex = currentProject.tasks.findIndex(t => t.id === taskId);
            
            if (taskIndex !== -1) {
                const task = currentProject.tasks[taskIndex];
                tasksToDelete.push({ task, index: taskIndex });
            } else {
                results.failureCount++;
                results.details.push({
                    taskId,
                    success: false,
                    reason: 'task_not_found'
                });
            }
        }

        // 排序並刪除
        tasksToDelete.sort((a, b) => b.index - a.index);
        
        for (const { task, index } of tasksToDelete) {
            currentProject.tasks.splice(index, 1);
            results.successCount++;
            results.details.push({
                taskId: task.id,
                taskName: task.text,
                success: true,
                action: 'deleted'
            });
        }

        // 更新狀態
        this.stateManager.setState({
            projects: [...state.projects]
        });

        return results;
    }

    // 顯示移動對話框
    showMoveDialog(taskIds, projects) {
        const projectOptions = projects.map(p => 
            `<option value="${p.id}">${p.name}</option>`
        ).join('');

        const content = `
            <p class="text-gray-700 mb-4">選擇要移動到的專案：</p>
            <select id="target-project-select" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="">請選擇專案...</option>
                ${projectOptions}
            </select>
        `;

        return new Modal({
            title: `移動 ${taskIds.length} 個任務`,
            content,
            buttons: [
                {
                    id: 'cancel',
                    text: '取消',
                    type: 'secondary'
                },
                {
                    id: 'move',
                    text: '移動',
                    type: 'primary',
                    onClick: (modal) => {
                        const select = modal.element.querySelector('#target-project-select');
                        const targetProjectId = select.value;
                        
                        if (!targetProjectId) {
                            Toast.warning('請選擇目標專案');
                            return false;
                        }
                        
                        this.bulkMove(targetProjectId, taskIds);
                        return true;
                    }
                }
            ]
        }).open();
    }

    // 顯示批量操作結果
    showBatchResults(results, operation) {
        const { successCount, failureCount } = results;
        
        if (failureCount === 0) {
            Toast.success(`成功${operation} ${successCount} 個任務`);
        } else if (successCount === 0) {
            Toast.error(`${operation}失敗，沒有任務被處理`);
        } else {
            Toast.warning(`部分${operation}成功：${successCount} 成功，${failureCount} 失敗`);
        }
    }

    // 綁定事件監聽器
    bindEvents() {
        // 批量完成按鈕
        const bulkCompleteBtn = document.getElementById('bulk-complete');
        if (bulkCompleteBtn) {
            bulkCompleteBtn.addEventListener('click', () => {
                this.bulkComplete();
            });
        }

        // 批量移動按鈕
        const bulkMoveBtn = document.getElementById('bulk-move');
        if (bulkMoveBtn) {
            bulkMoveBtn.addEventListener('click', () => {
                this.bulkMove();
            });
        }

        // 批量刪除按鈕
        const bulkDeleteBtn = document.getElementById('bulk-delete');
        if (bulkDeleteBtn) {
            bulkDeleteBtn.addEventListener('click', () => {
                this.bulkDelete();
            });
        }
    }

    // 檢查是否有操作正在進行
    isOperationInProgress() {
        return this.operationInProgress;
    }

    // 設置操作狀態
    setOperationInProgress(inProgress) {
        this.operationInProgress = inProgress;
    }
}

// 導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BulkOperationManager;
} else {
    window.BulkOperationManager = BulkOperationManager;
}
