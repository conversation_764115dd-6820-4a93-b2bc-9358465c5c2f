/**
 * Loading Component - V4.3
 * 載入狀態組件，支援多種載入樣式和覆蓋模式
 */

class Loading {
    static instances = new Map();
    static nextId = 1;

    constructor(options = {}) {
        this.options = {
            container: options.container || document.body,
            message: options.message || '載入中...',
            type: options.type || 'spinner', // spinner, dots, pulse, skeleton
            size: options.size || 'md', // sm, md, lg
            overlay: options.overlay !== false,
            backdrop: options.backdrop !== false,
            className: options.className || '',
            ...options
        };

        this.id = Loading.nextId++;
        this.element = null;
        this.isVisible = false;
    }

    show() {
        if (this.isVisible) return this;

        this.createElement();
        this.options.container.appendChild(this.element);
        
        // 添加進入動畫
        requestAnimationFrame(() => {
            this.element.classList.add('opacity-100');
        });

        this.isVisible = true;
        Loading.instances.set(this.id, this);

        return this;
    }

    hide() {
        if (!this.isVisible || !this.element) return this;

        // 添加退出動畫
        this.element.classList.remove('opacity-100');
        this.element.classList.add('opacity-0');

        setTimeout(() => {
            if (this.element && this.element.parentNode) {
                this.element.parentNode.removeChild(this.element);
            }
            this.element = null;
            this.isVisible = false;
            Loading.instances.delete(this.id);
        }, 300);

        return this;
    }

    createElement() {
        this.element = document.createElement('div');
        this.element.className = this.getLoadingClasses();
        this.element.innerHTML = this.getLoadingHTML();
    }

    getLoadingClasses() {
        const baseClasses = [
            'loading-overlay',
            'transition-opacity',
            'duration-300',
            'opacity-0'
        ];

        if (this.options.overlay) {
            baseClasses.push(
                'absolute',
                'inset-0',
                'flex',
                'items-center',
                'justify-center',
                'z-50'
            );

            if (this.options.backdrop) {
                baseClasses.push('bg-white', 'bg-opacity-80');
            }
        } else {
            baseClasses.push('inline-flex', 'items-center');
        }

        if (this.options.className) {
            baseClasses.push(this.options.className);
        }

        return baseClasses.join(' ');
    }

    getLoadingHTML() {
        const content = this.getLoadingContent();
        
        if (this.options.overlay) {
            return `
                <div class="loading-content flex flex-col items-center space-y-3 p-6 bg-white rounded-lg shadow-lg">
                    ${content}
                    ${this.options.message ? `<div class="loading-message text-sm text-gray-600">${this.options.message}</div>` : ''}
                </div>
            `;
        } else {
            return `
                <div class="loading-content flex items-center space-x-2">
                    ${content}
                    ${this.options.message ? `<span class="loading-message text-sm text-gray-600">${this.options.message}</span>` : ''}
                </div>
            `;
        }
    }

    getLoadingContent() {
        const sizeClasses = {
            sm: 'w-4 h-4',
            md: 'w-6 h-6',
            lg: 'w-8 h-8'
        };

        const size = sizeClasses[this.options.size];

        switch (this.options.type) {
            case 'spinner':
                return `<i class="fas fa-spinner animate-spin text-blue-500 ${size.replace('w-', 'text-').replace(' h-4', '').replace(' h-6', '').replace(' h-8', '')}"></i>`;
            
            case 'dots':
                return this.getDotsHTML();
            
            case 'pulse':
                return `<div class="animate-pulse bg-blue-500 rounded-full ${size}"></div>`;
            
            case 'skeleton':
                return this.getSkeletonHTML();
            
            default:
                return `<i class="fas fa-spinner animate-spin text-blue-500"></i>`;
        }
    }

    getDotsHTML() {
        const dotSize = this.options.size === 'sm' ? 'w-2 h-2' : 
                      this.options.size === 'lg' ? 'w-3 h-3' : 'w-2.5 h-2.5';

        return `
            <div class="flex space-x-1">
                <div class="${dotSize} bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0ms"></div>
                <div class="${dotSize} bg-blue-500 rounded-full animate-bounce" style="animation-delay: 150ms"></div>
                <div class="${dotSize} bg-blue-500 rounded-full animate-bounce" style="animation-delay: 300ms"></div>
            </div>
        `;
    }

    getSkeletonHTML() {
        return `
            <div class="animate-pulse space-y-2">
                <div class="h-4 bg-gray-300 rounded w-24"></div>
                <div class="h-3 bg-gray-300 rounded w-16"></div>
            </div>
        `;
    }

    // 更新訊息
    updateMessage(message) {
        if (this.element) {
            const messageEl = this.element.querySelector('.loading-message');
            if (messageEl) {
                messageEl.textContent = message;
            }
        }
        this.options.message = message;
    }

    // 靜態方法：快速顯示載入狀態
    static show(container, message, options = {}) {
        const loading = new Loading({
            container,
            message,
            ...options
        });
        return loading.show();
    }

    static hide(loadingInstance) {
        if (loadingInstance && typeof loadingInstance.hide === 'function') {
            loadingInstance.hide();
        }
    }

    static hideAll() {
        this.instances.forEach(instance => {
            instance.hide();
        });
    }

    // 全域載入遮罩
    static showGlobal(message = '載入中...', options = {}) {
        return this.show(document.body, message, {
            overlay: true,
            backdrop: true,
            className: 'fixed inset-0',
            ...options
        });
    }

    // 按鈕載入狀態
    static showButton(button, message = '', options = {}) {
        const originalContent = button.innerHTML;
        const originalDisabled = button.disabled;

        button.disabled = true;
        button.innerHTML = `
            <i class="fas fa-spinner animate-spin mr-2"></i>
            ${message || '處理中...'}
        `;

        return {
            hide: () => {
                button.innerHTML = originalContent;
                button.disabled = originalDisabled;
            }
        };
    }

    // 表單載入狀態
    static showForm(form, message = '提交中...', options = {}) {
        const formData = new FormData(form);
        const inputs = form.querySelectorAll('input, select, textarea, button');
        
        // 禁用所有表單元素
        inputs.forEach(input => {
            input.disabled = true;
        });

        const loading = this.show(form, message, {
            overlay: true,
            backdrop: true,
            ...options
        });

        return {
            hide: () => {
                loading.hide();
                inputs.forEach(input => {
                    input.disabled = false;
                });
            }
        };
    }

    // 卡片載入狀態
    static showCard(card, options = {}) {
        return this.show(card, '', {
            type: 'skeleton',
            overlay: true,
            backdrop: true,
            ...options
        });
    }

    // 列表載入狀態
    static showList(container, itemCount = 3, options = {}) {
        const skeletonItems = Array.from({ length: itemCount }, (_, i) => `
            <div class="animate-pulse p-4 border-b border-gray-200">
                <div class="flex space-x-3">
                    <div class="w-10 h-10 bg-gray-300 rounded-full"></div>
                    <div class="flex-1 space-y-2">
                        <div class="h-4 bg-gray-300 rounded w-3/4"></div>
                        <div class="h-3 bg-gray-300 rounded w-1/2"></div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = `
            <div class="skeleton-list">
                ${skeletonItems}
            </div>
        `;

        return {
            hide: () => {
                const skeleton = container.querySelector('.skeleton-list');
                if (skeleton) {
                    skeleton.remove();
                }
            }
        };
    }
}

// 導出組件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Loading;
}
