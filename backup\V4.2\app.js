document.addEventListener('DOMContentLoaded', () => {

    // --- DOM 元素選擇 ---
    const dom = {
        // AI Chat
        aiChatWidget: document.getElementById('ai-chat-widget'),
        chatHistory: document.getElementById('chat-history'),
        chatForm: document.getElementById('chat-form'),
        chatInput: document.getElementById('chat-input'),

        // Tabs
        tabButtons: document.querySelectorAll('.tab-btn'),
        projectBoardTabContent: document.getElementById('project-board-tab-content'),
        taskBoardTabContent: document.getElementById('task-board-tab-content'),
        settingsTabContent: document.getElementById('settings-tab-content'),

        // Project Board
        projectFilterButtons: document.getElementById('project-filter-buttons'),
        projectCardGrid: document.getElementById('project-card-grid'),

        // Task Board
        projectSelector: document.getElementById('project-selector'),
        addProjectForm: document.getElementById('add-project-form'),
        newProjectInput: document.getElementById('new-project-input'),
        projectProgressContainer: document.getElementById('project-progress-container'),
        projectProgressBar: document.getElementById('project-progress-bar'),
        projectProgressText: document.getElementById('project-progress-text'),
        addTaskForm: document.getElementById('add-task-form'),
        newTaskInput: document.getElementById('new-task-input'),
        todoTasksList: document.getElementById('todo-tasks-list'),
        completedTasksList: document.getElementById('completed-tasks-list'),

        // Settings
        apiKeyInput: document.getElementById('api-key-input'),
        saveApiKeyBtn: document.getElementById('save-api-key-btn'),
        exportJsonBtn: document.getElementById('export-json-btn'),
        importJsonInput: document.getElementById('import-json-input')
    };

    // --- 應用程式狀態管理 ---
    let state = {
        apiKey: null,
        currentProjectId: null,
        activeTab: 'project-board',
        projectBoardFilter: 'all',
        projects: []
    };

    const saveState = () => {
        localStorage.setItem('project-todo-app-state-v4.2', JSON.stringify(state));
    };

    const loadState = () => {
        const savedState = localStorage.getItem('project-todo-app-state-v4.2');
        if (savedState) {
            return JSON.parse(savedState);
        }
        return null;
    };

    // --- 渲染引擎 ---
    const render = () => {
        renderTabs();
        renderAIChat();
        if (state.activeTab === 'project-board') {
            renderProjectBoard();
        }
        if (state.activeTab === 'task-board') {
            renderTaskBoard();
        }
        if (state.activeTab === 'settings') {
            renderSettings();
        }
        saveState();
    };

    const renderTabs = () => {
        dom.projectBoardTabContent.classList.toggle('hidden', state.activeTab !== 'project-board');
        dom.taskBoardTabContent.classList.toggle('hidden', state.activeTab !== 'task-board');
        dom.settingsTabContent.classList.toggle('hidden', state.activeTab !== 'settings');
        dom.tabButtons.forEach(btn => {
            const tab = btn.dataset.tab;
            btn.classList.toggle('text-blue-500', tab === state.activeTab);
            btn.classList.toggle('border-b-2', tab === state.activeTab);
            btn.classList.toggle('border-blue-500', tab === state.activeTab);
        });
    };

    const renderAIChat = () => {
        const isLocked = !state.apiKey;
        dom.aiChatWidget.classList.toggle('opacity-60', isLocked);
        dom.chatInput.disabled = isLocked;
        dom.chatForm.querySelector('button').disabled = isLocked;
        if (isLocked) {
            dom.chatInput.placeholder = '請先在「設定」中提供 API 金鑰';
        } else {
            dom.chatInput.placeholder = '跟 AI 說話...';
        }
    };

    const renderTaskBoard = () => {
        renderProjectSelector();
        const project = state.projects.find(p => p.id === state.currentProjectId);
        if (!project) {
            dom.projectProgressContainer.classList.add('hidden');
            dom.addTaskForm.classList.add('hidden');
            dom.todoTasksList.innerHTML = '<p class="text-gray-500">請從「專案看板」選擇一個專案開始。</p>';
            dom.completedTasksList.innerHTML = '';
            return;
        }

        dom.projectProgressContainer.classList.remove('hidden');
        dom.addTaskForm.classList.remove('hidden');

        const projectProgress = calculateProjectProgress(project);
        dom.projectProgressBar.style.width = `${projectProgress.percentage}%`;
        dom.projectProgressText.textContent = `${projectProgress.percentage}% (${projectProgress.completed}/${projectProgress.total})`;

        dom.todoTasksList.innerHTML = '';
        dom.completedTasksList.innerHTML = '';
        project.tasks.forEach(task => {
            const taskCard = createTaskCard(task);
            if (task.completed) {
                dom.completedTasksList.appendChild(taskCard);
            } else {
                dom.todoTasksList.appendChild(taskCard);
            }
        });
    };

    const renderProjectBoard = () => {
        dom.projectCardGrid.innerHTML = '';
        const filteredProjects = state.projects.filter(p => {
            if (state.projectBoardFilter === 'all') return true;
            const progress = calculateProjectProgress(p);
            if (state.projectBoardFilter === 'inprogress') return progress.percentage < 100;
            if (state.projectBoardFilter === 'completed') return progress.percentage === 100;
            return false;
        });

        if (filteredProjects.length === 0) {
            dom.projectCardGrid.innerHTML = '<p class="text-gray-500 col-span-full">沒有符合條件的專案。</p>';
        }

        filteredProjects.forEach(p => {
            const card = createProjectCard(p);
            dom.projectCardGrid.appendChild(card);
        });

        dom.projectFilterButtons.querySelectorAll('button').forEach(btn => {
            btn.classList.toggle('bg-blue-500', btn.dataset.filter === state.projectBoardFilter);
            btn.classList.toggle('text-white', btn.dataset.filter === state.projectBoardFilter);
        });
    };

    const createProjectCard = (project) => {
        const card = document.createElement('div');
        const progress = calculateProjectProgress(project);
        const status = progress.percentage === 100 ? '已完成' : '執行中';
        const statusColor = progress.percentage === 100 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';

        card.className = 'bg-white p-6 rounded-lg shadow-md flex flex-col';
        card.innerHTML = `
            <div class="flex justify-between items-start mb-2">
                <h3 class="text-xl font-bold text-gray-800">${project.name}</h3>
                <span class="${statusColor} text-xs font-medium px-2.5 py-0.5 rounded-full">${status}</span>
            </div>
            <p class="text-sm text-gray-500 mb-4">${progress.completed} / ${progress.total} 項任務完成</p>
            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                <div class="bg-blue-500 h-2.5 rounded-full" style="width: ${progress.percentage}%"></div>
            </div>
            <button data-action="view-project" data-project-id="${project.id}" class="mt-auto w-full bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-black">查看專案</button>
        `;
        return card;
    };

    const renderProjectSelector = () => {
        dom.projectSelector.innerHTML = '';
        if (state.projects.length === 0) {
            const option = document.createElement('option');
            option.textContent = '尚無專案';
            option.disabled = true;
            dom.projectSelector.appendChild(option);
        } else {
            state.projects.forEach(p => {
                const option = document.createElement('option');
                option.value = p.id;
                option.textContent = p.name;
                if (p.id === state.currentProjectId) {
                    option.selected = true;
                }
                dom.projectSelector.appendChild(option);
            });
        }
    };

    const renderSettings = () => {
        dom.apiKeyInput.value = state.apiKey || '';
    };

    const createTaskCard = (task) => {
        const card = document.createElement('div');
        const progress = calculateTaskProgress(task);
        const progressText = progress.total > 0 ? `(${progress.completed}/${progress.total})` : '';
        const creationDate = new Date(task.creationDate).toLocaleDateString();
        const completionDate = task.completionDate ? new Date(task.completionDate).toLocaleDateString() : '';

        card.className = `task-card bg-white p-4 rounded-lg shadow-md max-w-4xl mx-auto ${task.completed ? 'opacity-70' : ''}`;
        card.dataset.taskId = task.id;
        card.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <input type="checkbox" data-action="toggle-complete" class="h-5 w-5 text-blue-500 rounded focus:ring-0" ${task.completed ? 'checked' : ''}>
                    <h4 class="font-bold text-lg ${task.completed ? 'line-through' : ''}">${task.text}</h4>
                    <span class="text-sm text-gray-500">${progressText}</span>
                </div>
                <button data-action="toggle-expand" class="text-gray-500 hover:text-blue-500">
                    <i class="fas fa-chevron-down transition-transform ${task.expanded ? 'rotate-180' : ''}"></i>
                </button>
            </div>
            <div class="text-xs text-gray-400 mt-1 ml-8">
                <span>創建於: ${creationDate}</span>
                ${completionDate ? `<span> | 完成於: ${completionDate}</span>` : ''}
            </div>
            <div data-subtask-container class="mt-4 pl-8 ${task.expanded ? '' : 'hidden'}">
                <div class="flex items-center justify-end space-x-2 mb-2">
                    <span class="text-xs font-semibold">篩選:</span>
                    <button data-action="filter-subtasks" data-filter="all" class="subtask-filter-btn px-2 py-0.5 text-xs rounded-md">全部</button>
                    <button data-action="filter-subtasks" data-filter="todo" class="subtask-filter-btn px-2 py-0.5 text-xs rounded-md">待辦</button>
                    <button data-action="filter-subtasks" data-filter="completed" class="subtask-filter-btn px-2 py-0.5 text-xs rounded-md">已完成</button>
                </div>
                <div class="space-y-2" data-subtask-list></div>
                <form data-action="add-subtask" class="flex mt-2">
                    <input type="text" class="flex-1 p-1 text-sm border rounded-l-md" placeholder="新增子任務..." required>
                    <button type="submit" class="bg-gray-200 text-gray-700 px-3 rounded-r-md hover:bg-gray-300">+</button>
                </form>
            </div>
        `;

        const subtaskListContainer = card.querySelector('[data-subtask-list]');
        const filteredSubtasks = task.subtasks.filter(st => {
            if (task.subtaskFilter === 'all') return true;
            if (task.subtaskFilter === 'todo') return !st.completed;
            if (task.subtaskFilter === 'completed') return st.completed;
            return false;
        });

        if (filteredSubtasks.length > 0) {
            filteredSubtasks.forEach(st => subtaskListContainer.appendChild(createSubtaskElement(st)));
        } else {
            subtaskListContainer.innerHTML = '<p class="text-xs text-gray-400 text-center">沒有符合條件的子任務。</p>';
        }
        
        card.querySelectorAll('.subtask-filter-btn').forEach(btn => {
            if (btn.dataset.filter === task.subtaskFilter) {
                btn.classList.add('bg-blue-500', 'text-white');
            }
        });

        return card;
    };

    const createSubtaskElement = (subtask) => {
        const el = document.createElement('div');
        const creationDate = new Date(subtask.creationDate).toLocaleDateString();
        const completionDate = subtask.completionDate ? new Date(subtask.completionDate).toLocaleDateString() : '';

        el.className = `p-2 rounded-md flex flex-col ${subtask.completed ? 'opacity-60 bg-gray-50' : 'bg-gray-100'}`;
        el.dataset.subtaskId = subtask.id;
        el.innerHTML = `
            <div class="flex items-center space-x-3">
                <input type="checkbox" data-action="toggle-subtask-complete" class="h-4 w-4 text-blue-500 rounded focus:ring-0" ${subtask.completed ? 'checked' : ''}>
                <span class="text-sm ${subtask.completed ? 'line-through' : ''}">${subtask.text}</span>
            </div>
            <div class="text-xs text-gray-400 mt-1 ml-7">
                <span>創建於: ${creationDate}</span>
                ${completionDate ? `<span> | 完成於: ${completionDate}</span>` : ''}
            </div>
        `;
        return el;
    };

    const addProject = (name) => {
        const newProject = { id: `proj-${Date.now()}`, name, tasks: [] };
        state.projects.push(newProject);
        state.currentProjectId = newProject.id;
        state.activeTab = 'task-board';
    };

    const addTask = (text) => {
        const project = state.projects.find(p => p.id === state.currentProjectId);
        if (project) {
            project.tasks.push({ id: `task-${Date.now()}`, text, completed: false, expanded: true, subtaskFilter: 'all', creationDate: new Date().toISOString(), completionDate: null, subtasks: [] });
        }
    };

    const addSubtask = (taskId, text) => {
        const task = findTask(taskId);
        if (task) {
            task.subtasks.push({ id: `sub-${Date.now()}`, text, completed: false, creationDate: new Date().toISOString(), completionDate: null });
        }
    };

    const toggleTaskProperty = (taskId, property) => {
        const task = findTask(taskId);
        if (!task) return;
        if (property === 'completed') {
            task.completed = !task.completed;
            task.completionDate = task.completed ? new Date().toISOString() : null;
        } else if (property === 'expanded') {
            task.expanded = !task.expanded;
        }
    };

    const setSubtaskFilter = (taskId, filter) => {
        const task = findTask(taskId);
        if (task) {
            task.subtaskFilter = filter;
        }
    };

    const toggleSubtaskCompletion = (taskId, subtaskId) => {
        const task = findTask(taskId);
        const subtask = task?.subtasks.find(st => st.id === subtaskId);
        if (subtask) {
            subtask.completed = !subtask.completed;
            subtask.completionDate = subtask.completed ? new Date().toISOString() : null;
        }
    };

    const findTask = (taskId) => {
        const project = state.projects.find(p => p.id === state.currentProjectId);
        return project?.tasks.find(t => t.id === taskId);
    };

    const calculateProjectProgress = (project) => {
        if (!project || project.tasks.length === 0) return { percentage: 0, completed: 0, total: 0 };
        const total = project.tasks.length;
        const completed = project.tasks.filter(t => t.completed).length;
        return { percentage: total === 0 ? 0 : Math.round((completed / total) * 100), completed, total };
    };

    const calculateTaskProgress = (task) => {
        if (!task.subtasks || task.subtasks.length === 0) return { completed: 0, total: 0 };
        const total = task.subtasks.length;
        const completed = task.subtasks.filter(st => st.completed).length;
        return { completed, total };
    };

    const addMessageToChat = (sender, message) => {
        const msgDiv = document.createElement('div');
        msgDiv.className = `text-sm p-2 rounded-lg mb-2 max-w-xs ${sender === 'user' ? 'bg-blue-100 self-end' : 'bg-gray-200 self-start'}`;
        msgDiv.innerHTML = message;
        dom.chatHistory.appendChild(msgDiv);
        dom.chatHistory.scrollTop = dom.chatHistory.scrollHeight;
    };

    const callGeminiAPI = async (prompt) => {
        addMessageToChat('user', prompt);
        addMessageToChat('ai', '<i class="fas fa-spinner fa-spin"></i> 思考中...');

        const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${state.apiKey}`;
        const fullPrompt = `你是一個任務管理助理。根據使用者的要求，回傳一個 JSON 物件。JSON 必須包含 'action' (例如 'addTask', 'addProjectPlan') 和 'payload')。\n\n模式一：快速新增。若使用者指令單純，回傳單一動作 JSON。例如使用者說：「新增任務：買牛奶」，你回傳：{"action": "addTask", "payload": {"text": "買牛奶"}}。\n\n模式二：完整規劃。若使用者要求規劃，請回傳一個符合我們應用程式資料結構的完整專案物件 JSON。專案物件應包含 id, name, 和一個 tasks 陣列，tasks陣列中可包含subtasks。例如使用者說：「規劃一個為期一週的東京旅遊」，你回傳：{"action": "addProjectPlan", "payload": {"id": "proj-${Date.now()}", "name": "東京一週旅遊", "tasks": [{"id":"task-${Date.now() + 1}","text":"行前準備","completed":false,"expanded":true,"subtaskFilter":"all","creationDate":"${new Date().toISOString()}","completionDate":null,"subtasks":[{"id":"sub-${Date.now() + 2}","text":"購買機票","completed":false,"creationDate":"${new Date().toISOString()}","completionDate":null}]}]}}\n\n使用者說：「${prompt}」`;

        try {
            const response = await fetch(API_URL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ contents: [{ parts: [{ text: fullPrompt }] }] })
            });

            dom.chatHistory.removeChild(dom.chatHistory.lastChild);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`API 請求失敗: ${errorData.error.message}`);
            }

            const data = await response.json();
            const aiResponseText = data.candidates[0].content.parts[0].text;
            const jsonMatch = aiResponseText.match(/\`\`\`json([\s\S]*?)\`\`\`/);
            if (!jsonMatch || !jsonMatch[1]) throw new Error("AI 未回傳有效的 JSON 格式。");
            const command = JSON.parse(jsonMatch[1]);
            executeAIAction(command);

        } catch (error) {
            console.error('Gemini API Error:', error);
            addMessageToChat('ai', `抱歉，AI 功能出現錯誤。<br><small class="text-red-500">${error.message}</small>`);
        }
    };

    const executeAIAction = (command) => {
        if (!command || !command.action) return;
        switch (command.action) {
            case 'addTask':
                if (state.currentProjectId) {
                    addTask(command.payload.text);
                    addMessageToChat('ai', `好的，已在目前專案新增任務：「${command.payload.text}」。`);
                } else {
                    addMessageToChat('ai', '請先選擇一個專案才能新增任務。');
                }
                break;
            case 'addProjectPlan':
                const newProject = { ...command.payload, id: `proj-${Date.now()}` };
                state.projects.push(newProject);
                state.currentProjectId = newProject.id;
                state.activeTab = 'task-board';
                addMessageToChat('ai', `好的，已為您建立新專案：「${newProject.name}」。`);
                break;
            default:
                addMessageToChat('ai', '抱歉，我無法理解這個指令。');
        }
        render();
    };

    const addEventListeners = () => {
        dom.tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                state.activeTab = btn.dataset.tab;
                render();
            });
        });

        dom.projectFilterButtons.addEventListener('click', (e) => {
            if (e.target.tagName === 'BUTTON') {
                state.projectBoardFilter = e.target.dataset.filter;
                render();
            }
        });

        dom.projectBoardTabContent.addEventListener('click', (e) => {
            const viewButton = e.target.closest('[data-action="view-project"]');
            if (viewButton) {
                state.currentProjectId = viewButton.dataset.projectId;
                state.activeTab = 'task-board';
                render();
            }
        });

        dom.projectSelector.addEventListener('change', (e) => {
            state.currentProjectId = e.target.value;
            render();
        });

        dom.addProjectForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const name = dom.newProjectInput.value.trim();
            if (name) { addProject(name); dom.newProjectInput.value = ''; render(); }
        });

        dom.addTaskForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const text = dom.newTaskInput.value.trim();
            if (text) { addTask(text); dom.newTaskInput.value = ''; render(); }
        });

        const taskBoardContent = dom.taskBoardTabContent;
        taskBoardContent.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (!action) return;

            const taskCard = e.target.closest('.task-card');
            if (!taskCard) return;
            const taskId = taskCard.dataset.taskId;

            if (action === 'toggle-complete') {
                toggleTaskProperty(taskId, 'completed');
            } else if (action === 'toggle-expand') {
                toggleTaskProperty(taskId, 'expanded');
            } else if (action === 'toggle-subtask-complete') {
                const subtaskEl = e.target.closest('[data-subtask-id]');
                if (subtaskEl) {
                    toggleSubtaskCompletion(taskId, subtaskEl.dataset.subtaskId);
                }
            } else if (action === 'filter-subtasks') {
                setSubtaskFilter(taskId, e.target.dataset.filter);
            }
            render();
        });

        taskBoardContent.addEventListener('submit', (e) => {
            e.preventDefault();
            if (e.target.dataset.action === 'add-subtask') {
                const taskCard = e.target.closest('.task-card');
                const taskId = taskCard.dataset.taskId;
                const input = e.target.querySelector('input');
                const text = input.value.trim();
                if (text) { addSubtask(taskId, text); input.value = ''; render(); }
            }
        });

        dom.saveApiKeyBtn.addEventListener('click', () => {
            const key = dom.apiKeyInput.value.trim();
            state.apiKey = key ? key : null;
            render();
            alert(key ? 'API 金鑰已儲存。' : 'API 金鑰已清除。');
        });

        dom.exportJsonBtn.addEventListener('click', () => {
            const dataStr = JSON.stringify(state, null, 2);
            const blob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url; a.download = 'project-backup-v4.2.json'; a.click();
            URL.revokeObjectURL(url);
        });

        dom.importJsonInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = (event) => {
                try {
                    const importedState = JSON.parse(event.target.result);
                    if (importedState && importedState.projects && typeof importedState.apiKey !== 'undefined') {
                        state = importedState;
                        saveState();
                        alert('匯入成功！頁面將會重新整理。');
                        location.reload();
                    } else { alert('檔案格式不符！'); }
                } catch (err) { alert('讀取檔案失敗，請確認檔案為正確的 JSON 格式。'); console.error(err); }
            };
            reader.readAsText(file);
            e.target.value = '';
        });

        dom.chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const userInput = dom.chatInput.value.trim();
            if (userInput && state.apiKey) {
                callGeminiAPI(userInput);
                dom.chatInput.value = '';
            }
        });
    };

    const init = () => {
        const savedState = loadState();
        if (savedState) {
            state = savedState;
        } else {
            addProject('歡迎使用 V4.2！');
            state.activeTab = 'task-board';
            addTask('點擊上方的「專案看板」查看所有專案');
            addTask('點擊「設定」來配置您的 API 金鑰');
        }
        addEventListeners();
        render();
    };

    init();
});