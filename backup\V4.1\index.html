<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>專案代辦事項管理 V4</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        /* 簡單的滾動條樣式，使其不那麼突兀 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="flex h-screen bg-gray-100 font-sans">

    <!-- AI 聊天視窗 (固定在左側) -->
    <aside id="ai-chat-widget" class="w-80 flex flex-col bg-gray-200 p-4 border-r border-gray-300">
        <h2 class="text-xl font-bold text-center mb-4">AI 助理</h2>
        <div id="chat-history" class="flex-1 bg-white rounded-lg p-2 overflow-y-auto mb-4 border"></div>
        <form id="chat-form" class="flex">
            <input type="text" id="chat-input" class="flex-1 p-2 border rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="跟 AI 說話..." disabled>
            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-r-lg hover:bg-blue-600 disabled:bg-gray-400"><i class="fas fa-paper-plane"></i></button>
        </form>
    </aside>

    <!-- 主內容區 -->
    <div class="flex-1 flex flex-col">
        <!-- 分頁導航 -->
        <nav class="bg-white shadow-md">
            <div class="container mx-auto px-6">
                <div class="flex">
                    <button data-tab="board" class="tab-btn py-4 px-6 block hover:text-blue-500 focus:outline-none">專案看板</button>
                    <button data-tab="settings" class="tab-btn py-4 px-6 block hover:text-blue-500 focus:outline-none">設定</button>
                </div>
            </div>
        </nav>

        <!-- 分頁內容容器 -->
        <main class="flex-1 p-6 overflow-y-auto">
            <!-- 專案看板 -->
            <div id="board-tab-content">
                <!-- 頂部操作區 -->
                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center space-x-4">
                        <select id="project-selector" class="p-2 border rounded-lg"></select>
                        <form id="add-project-form" class="flex">
                            <input id="new-project-input" type="text" class="p-2 border rounded-l-lg" placeholder="新增專案..." required>
                            <button type="submit" class="bg-blue-500 text-white px-4 rounded-r-lg hover:bg-blue-600">+</button>
                        </form>
                    </div>
                </div>
                <!-- 進度條 -->
                <div id="project-progress-container" class="mb-6">
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-semibold text-gray-700">專案進度</span>
                        <span id="project-progress-text" class="text-sm font-semibold text-gray-700">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div id="project-progress-bar" class="bg-green-500 h-2.5 rounded-full transition-all duration-500" style="width: 0%"></div>
                    </div>
                </div>
                <!-- 新增主任務 -->
                <form id="add-task-form" class="flex mb-6">
                    <input id="new-task-input" type="text" class="flex-1 p-2 border rounded-l-lg" placeholder="新增一個主任務..." required>
                    <button type="submit" class="bg-blue-500 text-white px-4 rounded-r-lg hover:bg-blue-600">新增任務</button>
                </form>
                <!-- 任務列表 -->
                <div id="tasks-wrapper" class="space-y-6">
                    <div>
                        <h3 class="text-lg font-semibold mb-2 text-gray-800">待辦任務</h3>
                        <div id="todo-tasks-list" class="space-y-4"></div>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold mb-2 text-gray-800">已完成任務</h3>
                        <div id="completed-tasks-list" class="space-y-4"></div>
                    </div>
                </div>
            </div>

            <!-- 設定頁 -->
            <div id="settings-tab-content" class="hidden">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- API 金鑰設定卡片 -->
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-semibold mb-4">Gemini API 金鑰</h3>
                        <p class="text-gray-600 mb-4 text-sm">請至 <a href="https://aistudio.google.com/app/apikey" target="_blank" class="text-blue-500 hover:underline">Google AI Studio</a> 取得您的 Gemini API 金鑰以啟用 AI 助理功能。</p>
                        <div class="flex">
                            <input type="password" id="api-key-input" class="flex-1 p-2 border rounded-l-lg" placeholder="貼上您的 API 金鑰">
                            <button id="save-api-key-btn" class="bg-blue-500 text-white px-4 rounded-r-lg hover:bg-blue-600">儲存</button>
                        </div>
                    </div>
                    <!-- 資料管理卡片 -->
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-semibold mb-4">資料管理</h3>
                        <p class="text-gray-600 mb-4 text-sm">將您的所有專案、任務及設定匯出成一個 JSON 檔案進行備份，或從備份檔案中還原。</p>
                        <div class="flex space-x-4">
                            <button id="export-json-btn" class="w-full bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">匯出資料</button>
                            <label for="import-json-input" class="w-full bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 text-center cursor-pointer">匯入資料</label>
                            <input type="file" id="import-json-input" accept=".json" class="hidden">
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="app.js"></script>
</body>
</html>
