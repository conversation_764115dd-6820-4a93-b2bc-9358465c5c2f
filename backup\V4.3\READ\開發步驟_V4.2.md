# 專案代辦事項管理 - V4.2 開發步驟

## 總覽

本文件旨在將 V4.2 的功能規劃轉化為具體的開發任務。核心是引入「專案看板」，並對「任務看板」進行精細化升級。

---

## 階段一：HTML 結構更新

**目標**：調整 HTML 以容納新的「專案看板」分頁和增強的「任務看板」功能。

1.  **分頁導航 (`<nav>`)**：
    - 新增一個 `data-tab="project-board"` 的按鈕，使其成為三分頁佈局。
2.  **分頁內容容器 (`<main>`)**：
    - 新增一個 `#project-board-tab-content` 的 `<div>`，用於容納專案看板。
    - 將原有的 `#board-tab-content` 更名為 `#task-board-tab-content`。
3.  **專案看板內容 (`#project-board-tab-content`)**：
    - 頂部建立一個篩選按鈕組 (`<div>`)，包含「全部」、「執行中」、「已完成」三個按鈕。
    - 下方建立一個 `<div>` 作為專案卡片的網格容器 (`id="project-card-grid"`)。
4.  **任務看板內容 (`#task-board-tab-content`)**：
    - (無需重大結構變更，主要調整將在 JS 的 `createTaskCard` 中完成)。

---

## 階段二：JavaScript 邏輯重構

**目標**：更新狀態管理、渲染引擎和核心功能，以支援 V4.2 的新特性。

1.  **狀態管理 (`State Management`)**：
    - 更新 `state` 物件的初始結構，加入 `projectBoardFilter: 'all'`，並將 `activeTab` 預設為 `project-board`。
    - 在 `addTask` 函式中，為每個 `task` 物件新增 `subtaskFilter: 'all'` 屬性。
    - 在 `addSubtask` 函式中，為每個 `subtask` 物件新增 `creationDate` 和 `completionDate` 屬性。

2.  **渲染引擎 (`Render Engine`)**：
    - **主 `render()` 函式**：現在需要根據 `state.activeTab` 的值，呼叫 `renderProjectBoard()` 或 `renderTaskBoard()`。
    - **`renderProjectBoard()` (新增)**：
        - 讀取 `state.projectBoardFilter` 的值。
        - 遍歷 `state.projects`，根據篩選條件過濾專案。
        - 對於每個符合條件的專案，呼叫 `createProjectCard()` 來生成 DOM 元素，並將其附加到網格容器中。
    - **`renderTaskBoard()` (重構)**：
        - 此函式現在專注於渲染單一專案的任務列表，其邏輯與 V4 的 `renderBoard` 類似。

3.  **DOM 元素工廠 (`Element Factory`)**：
    - **`createProjectCard(project)` (新增)**：
        - 回傳一個專案卡片的 DOM 元素。
        - 卡片內部使用 Tailwind CSS class，包含專案名稱、進度條和「查看專案」按鈕。
        - 按鈕上需綁定 `data-project-id` 屬性，以便事件監聽。
    - **`createTaskCard(task)` (升級)**：
        - 在卡片內部，新增一組用於篩選子任務的按鈕（「全部」、「待辦」、「已完成」）。
        - 根據 `task.subtaskFilter` 的值，為當前活動的篩選按鈕添加高亮樣式。
        - 子任務列表現在需要被明確地渲染到兩個 `<div>` 中：一個用於待辦，一個用於已完成。
        - 在渲染子任務時，根據 `task.subtaskFilter` 的值來決定顯示哪些子任務。
        - **樣式**: 為卡片本身添加 `max-w-4xl mx-auto` 等 class，使其在寬螢幕上居中並限制最大寬度。
    - **`createSubtaskElement(subtask)` (升級)**：
        - 在子任務項目中，新增顯示 `creationDate` 和 `completionDate` 的 `<span>` 元素。

4.  **事件監聽 (`Event Listeners`)**：
    - **專案看板篩選**：監聽篩選按鈕的點擊，更新 `state.projectBoardFilter`，然後呼叫 `render()`。
    - **查看專案**：在專案看板上使用事件委派，監聽「查看專案」按鈕的點擊。獲取 `data-project-id`，將 `state.currentProjectId` 更新為該 ID，並將 `state.activeTab` 切換到 `task-board`，最後呼叫 `render()`。
    - **子任務篩選**：在任務看板上使用事件委派，監聽任務卡片內部的篩選按鈕點擊。找到對應的任務 ID，更新其 `subtaskFilter` 屬性，然後呼叫 `render()`。
