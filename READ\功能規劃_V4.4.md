# 功能規劃 V4.4 - 任務詳述顯示與編輯功能

## 📋 版本概述

**版本號**: V4.4  
**發布日期**: 2025-08-07  
**主要功能**: 任務詳述顯示與編輯功能  
**基於版本**: V4.3

## 🎯 核心目標

### 主要改進
1. **任務詳述顯示**: 在主任務和子任務卡片中條件性顯示 `description` 欄位
2. **任務編輯功能**: 提供完整的主任務和子任務編輯界面，包括標題、詳述編輯和刪除功能
3. **用戶體驗優化**: 通過圖示和模態框提供直觀的編輯體驗
4. **子任務增強**: 為子任務添加與主任務相同的編輯和描述功能

### 設計原則
- **向後兼容**: 保持現有新增任務功能不變
- **條件顯示**: 只在有詳述內容時才顯示
- **直觀操作**: 使用圖示觸發編輯功能
- **安全操作**: 刪除功能需要確認

## 🔧 功能詳細規劃

### 1. 任務資料結構調整

#### 1.1 現有結構
```javascript
const task = {
    id: "task-xxx",
    text: "任務簡述",        // 主要顯示內容
    description: "",        // 目前未使用
    priority: "medium",
    tags: [],
    completed: false,
    // ...其他欄位
};
```

#### 1.2 功能定義
- **text**: 任務簡述（必填，主要顯示）
- **description**: 任務詳述（選填，條件顯示）

#### 1.3 子任務結構調整
```javascript
const subtask = {
    id: "sub-xxx",
    text: "子任務簡述",      // 主要顯示內容
    description: "",        // 新增：子任務詳述
    completed: false,
    creationDate: "...",
    completionDate: null
};
```

### 2. UI 顯示邏輯

#### 2.1 任務卡片顯示
```
┌─────────────────────────────────────┐
│ ☐ 任務簡述 (text)            [編輯] │
│   任務詳述 (description)            │  ← 條件顯示
│   創建於: 2025-08-07                │
└─────────────────────────────────────┘
```

#### 2.2 顯示條件
- **有詳述**: 顯示詳述內容，字體較小，顏色較淡
- **無詳述**: 不顯示詳述區域，節省空間

#### 2.3 編輯圖示
- **位置**: 任務卡片右上角
- **圖示**: `fas fa-edit` 或 `fas fa-pencil-alt`
- **樣式**: 灰色，hover 時變藍色
- **功能**: 點擊開啟編輯模態框

### 3. 編輯功能設計

#### 3.1 主任務編輯模態框
```
┌─────────────────────────────────────┐
│ 編輯任務                      [×]   │
├─────────────────────────────────────┤
│ 任務標題:                           │
│ ┌─────────────────────────────────┐ │
│ │ [任務簡述輸入框]                │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 任務詳述:                           │
│ ┌─────────────────────────────────┐ │
│ │ [多行文本輸入框]                │ │
│ │                                 │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [刪除任務] [取消] [儲存]            │
└─────────────────────────────────────┘
```

#### 3.2 表單欄位
- **任務標題**: 單行輸入框，必填
- **任務詳述**: 多行文本框（textarea），選填
- **操作按鈕**: 刪除、取消、儲存

#### 3.3 操作邏輯
- **儲存**: 驗證標題非空，更新任務資料
- **取消**: 關閉模態框，不儲存變更
- **刪除**: 顯示確認對話框，確認後刪除任務

#### 3.3 子任務編輯模態框
```
┌─────────────────────────────────────┐
│ 編輯子任務                    [×]   │
├─────────────────────────────────────┤
│ 子任務標題:                         │
│ ┌─────────────────────────────────┐ │
│ │ [子任務簡述輸入框]              │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 子任務詳述:                         │
│ ┌─────────────────────────────────┐ │
│ │ [多行文本輸入框]                │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [刪除子任務] [取消] [儲存]          │
└─────────────────────────────────────┘
```

#### 3.4 子任務編輯操作
- **儲存**: 驗證標題非空，更新子任務資料
- **取消**: 關閉模態框，不儲存變更
- **刪除**: 顯示確認對話框，確認後刪除子任務並更新主任務狀態

### 4. 技術實現要點

#### 4.1 模態框組件
- 使用現有 `Modal` 組件
- 自定義編輯表單內容
- 處理表單驗證和提交

#### 4.2 事件處理
- 編輯圖示點擊事件
- 表單提交事件
- 刪除確認事件

#### 4.3 資料更新
- 更新任務物件
- 觸發重新渲染
- 顯示操作結果提示

## 🎨 UI/UX 設計

### 1. 視覺設計
- **編輯圖示**: 小巧、不突兀
- **詳述文字**: 較小字體，灰色
- **模態框**: 清晰的表單佈局

### 2. 互動設計
- **hover 效果**: 編輯圖示 hover 變色
- **焦點管理**: 模態框開啟時自動聚焦標題輸入框
- **鍵盤支援**: ESC 關閉模態框

### 3. 響應式設計
- **手機端**: 模態框適應小螢幕
- **平板端**: 保持良好的觸控體驗

## 📱 功能流程

### 1. 查看任務詳述
```
用戶查看任務列表
    ↓
系統檢查任務是否有 description
    ↓
有詳述 → 在卡片中顯示詳述內容
無詳述 → 只顯示簡述
```

### 2. 編輯任務
```
用戶點擊編輯圖示
    ↓
開啟編輯模態框
    ↓
預填現有資料
    ↓
用戶修改內容
    ↓
點擊儲存 → 驗證 → 更新資料 → 關閉模態框
點擊取消 → 直接關閉模態框
點擊刪除 → 確認對話框 → 刪除任務
```

## 🔄 版本升級計劃

### 1. 檔案備份
- 備份 V4.3 版本到 `backup/V4.3/`
- 保留完整的檔案結構

### 2. 版本號更新
- 更新所有檔案中的版本標識
- 更新註解和說明文檔

### 3. 功能實現順序
1. 備份現有檔案
2. 更新版本號
3. 實現詳述顯示功能
4. 實現編輯功能
5. 測試和驗證

## 🧪 測試計劃

### 1. 功能測試
- [ ] 詳述條件顯示正確
- [ ] 編輯圖示正常顯示
- [ ] 模態框正常開啟/關閉
- [ ] 表單驗證正確
- [ ] 資料儲存正確
- [ ] 刪除功能正確

### 2. 兼容性測試
- [ ] 現有功能不受影響
- [ ] 新增任務功能正常
- [ ] 子任務功能正常
- [ ] 搜尋功能正常

### 3. UI/UX 測試
- [ ] 視覺效果符合預期
- [ ] 互動體驗流暢
- [ ] 響應式設計正確

## 📝 開發注意事項

1. **保持向後兼容**: 不影響現有功能
2. **錯誤處理**: 妥善處理編輯和刪除操作的錯誤
3. **性能考慮**: 避免不必要的重新渲染
4. **代碼品質**: 保持代碼結構清晰，添加適當註解
5. **用戶體驗**: 提供清晰的操作反饋和錯誤提示
