document.addEventListener('DOMContentLoaded', () => {

    // --- DOM 元素選擇 ---
    const dom = {
        // AI Chat
        aiChatWidget: document.getElementById('ai-chat-widget'),
        chatHistory: document.getElementById('chat-history'),
        chatForm: document.getElementById('chat-form'),
        chatInput: document.getElementById('chat-input'),

        // Tabs
        tabButtons: document.querySelectorAll('.tab-btn'),
        boardTabContent: document.getElementById('board-tab-content'),
        settingsTabContent: document.getElementById('settings-tab-content'),

        // Board
        projectSelector: document.getElementById('project-selector'),
        addProjectForm: document.getElementById('add-project-form'),
        newProjectInput: document.getElementById('new-project-input'),
        projectProgressContainer: document.getElementById('project-progress-container'),
        projectProgressBar: document.getElementById('project-progress-bar'),
        projectProgressText: document.getElementById('project-progress-text'),
        addTaskForm: document.getElementById('add-task-form'),
        newTaskInput: document.getElementById('new-task-input'),
        todoTasksList: document.getElementById('todo-tasks-list'),
        completedTasksList: document.getElementById('completed-tasks-list'),

        // Settings
        apiKeyInput: document.getElementById('api-key-input'),
        saveApiKeyBtn: document.getElementById('save-api-key-btn'),
        exportJsonBtn: document.getElementById('export-json-btn'),
        importJsonInput: document.getElementById('import-json-input'),
    };

    // --- 應用程式狀態管理 ---
    let state = {
        apiKey: null,
        currentProjectId: null,
        activeTab: 'board',
        projects: [],
    };

    const saveState = () => {
        localStorage.setItem('project-todo-app-state-v4', JSON.stringify(state));
    };

    const loadState = () => {
        const savedState = localStorage.getItem('project-todo-app-state-v4');
        if (savedState) {
            return JSON.parse(savedState);
        }
        return null;
    };

    // --- 渲染引擎 ---
    const render = () => {
        renderTabs();
        renderAIChat();
        if (state.activeTab === 'board') {
            renderBoard();
        }
        if (state.activeTab === 'settings') {
            renderSettings();
        }
        saveState();
    };

    const renderTabs = () => {
        dom.boardTabContent.classList.toggle('hidden', state.activeTab !== 'board');
        dom.settingsTabContent.classList.toggle('hidden', state.activeTab !== 'settings');
        dom.tabButtons.forEach(btn => {
            const tab = btn.dataset.tab;
            btn.classList.toggle('text-blue-500', tab === state.activeTab);
            btn.classList.toggle('border-b-2', tab === state.activeTab);
            btn.classList.toggle('border-blue-500', tab === state.activeTab);
        });
    };

    const renderAIChat = () => {
        const isLocked = !state.apiKey;
        dom.aiChatWidget.classList.toggle('opacity-60', isLocked);
        dom.chatInput.disabled = isLocked;
        dom.chatForm.querySelector('button').disabled = isLocked;
        if (isLocked) {
            dom.chatInput.placeholder = '請先在「設定」中提供 API 金鑰';
        } else {
            dom.chatInput.placeholder = '跟 AI 說話...';
        }
    };

    const renderBoard = () => {
        renderProjectSelector();
        const project = state.projects.find(p => p.id === state.currentProjectId);
        if (!project) {
            dom.projectProgressContainer.classList.add('hidden');
            dom.addTaskForm.classList.add('hidden');
            dom.todoTasksList.innerHTML = '<p class="text-gray-500">請選擇或建立一個專案開始。</p>';
            dom.completedTasksList.innerHTML = '';
            return;
        }

        dom.projectProgressContainer.classList.remove('hidden');
        dom.addTaskForm.classList.remove('hidden');

        // Render progress
        const projectProgress = calculateProjectProgress(project);
        dom.projectProgressBar.style.width = `${projectProgress.percentage}%`;
        dom.projectProgressText.textContent = `${projectProgress.percentage}% (${projectProgress.completed}/${projectProgress.total})`;

        // Render tasks
        dom.todoTasksList.innerHTML = '';
        dom.completedTasksList.innerHTML = '';
        project.tasks.forEach(task => {
            const taskCard = createTaskCard(task);
            if (task.completed) {
                dom.completedTasksList.appendChild(taskCard);
            } else {
                dom.todoTasksList.appendChild(taskCard);
            }
        });
    };

    const renderProjectSelector = () => {
        dom.projectSelector.innerHTML = '';
        if (state.projects.length === 0) {
            const option = document.createElement('option');
            option.textContent = '尚無專案';
            option.disabled = true;
            dom.projectSelector.appendChild(option);
        } else {
            state.projects.forEach(p => {
                const option = document.createElement('option');
                option.value = p.id;
                option.textContent = p.name;
                if (p.id === state.currentProjectId) {
                    option.selected = true;
                }
                dom.projectSelector.appendChild(option);
            });
        }
    };

    const renderSettings = () => {
        dom.apiKeyInput.value = state.apiKey || '';
    };

    // --- DOM 元素工廠 ---
    const createTaskCard = (task) => {
        const card = document.createElement('div');
        const progress = calculateTaskProgress(task);
        const progressText = progress.total > 0 ? `(${progress.completed}/${progress.total})` : '';
        const creationDate = new Date(task.creationDate).toLocaleDateString();
        const completionDate = task.completionDate ? new Date(task.completionDate).toLocaleDateString() : '';

        card.className = `task-card bg-white p-4 rounded-lg shadow-md ${task.completed ? 'opacity-70' : ''}`;
        card.dataset.taskId = task.id;
        card.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <input type="checkbox" data-action="toggle-complete" class="h-5 w-5 text-blue-500 rounded" ${task.completed ? 'checked' : ''}>
                    <h4 class="font-bold text-lg ${task.completed ? 'line-through' : ''}">${task.text}</h4>
                    <span class="text-sm text-gray-500">${progressText}</span>
                </div>
                <button data-action="toggle-expand" class="text-gray-500 hover:text-blue-500">
                    <i class="fas fa-chevron-down transition-transform ${task.expanded ? 'rotate-180' : ''}"></i>
                </button>
            </div>
            <div class="text-xs text-gray-400 mt-1">
                <span>創建於: ${creationDate}</span>
                ${completionDate ? `<span> | 完成於: ${completionDate}</span>` : ''}
            </div>
            <div data-subtask-container class="mt-4 pl-8 ${task.expanded ? '' : 'hidden'}">
                <div class="space-y-2"></div>
                <form data-action="add-subtask" class="flex mt-2">
                    <input type="text" class="flex-1 p-1 text-sm border rounded-l-md" placeholder="新增子任務..." required>
                    <button type="submit" class="bg-gray-200 text-gray-700 px-3 rounded-r-md hover:bg-gray-300">+</button>
                </form>
            </div>
        `;

        const subtaskList = card.querySelector('.space-y-2');
        task.subtasks.forEach(st => subtaskList.appendChild(createSubtaskElement(st)));

        return card;
    };

    const createSubtaskElement = (subtask) => {
        const el = document.createElement('div');
        el.className = `flex items-center space-x-3 ${subtask.completed ? 'opacity-60' : ''}`;
        el.dataset.subtaskId = subtask.id;
        el.innerHTML = `
            <input type="checkbox" data-action="toggle-subtask-complete" class="h-4 w-4 text-blue-500 rounded" ${subtask.completed ? 'checked' : ''}>
            <span class="text-sm ${subtask.completed ? 'line-through' : ''}">${subtask.text}</span>
        `;
        return el;
    };

    // --- 核心功能函式 ---
    const addProject = (name) => {
        const newProject = { id: `proj-${Date.now()}`, name, tasks: [] };
        state.projects.push(newProject);
        state.currentProjectId = newProject.id;
    };

    const addTask = (text) => {
        const project = state.projects.find(p => p.id === state.currentProjectId);
        if (project) {
            project.tasks.push({ id: `task-${Date.now()}`, text, completed: false, expanded: true, creationDate: new Date().toISOString(), completionDate: null, subtasks: [] });
        }
    };

    const addSubtask = (taskId, text) => {
        const task = findTask(taskId);
        if (task) {
            task.subtasks.push({ id: `sub-${Date.now()}`, text, completed: false, creationDate: new Date().toISOString(), completionDate: null });
        }
    };

    const toggleTaskProperty = (taskId, property) => {
        const task = findTask(taskId);
        if (!task) return;
        if (property === 'completed') {
            task.completed = !task.completed;
            task.completionDate = task.completed ? new Date().toISOString() : null;
        } else if (property === 'expanded') {
            task.expanded = !task.expanded;
        }
    };

    const toggleSubtaskCompletion = (taskId, subtaskId) => {
        const task = findTask(taskId);
        const subtask = task?.subtasks.find(st => st.id === subtaskId);
        if (subtask) {
            subtask.completed = !subtask.completed;
            subtask.completionDate = subtask.completed ? new Date().toISOString() : null;
        }
    };

    const findTask = (taskId) => {
        const project = state.projects.find(p => p.id === state.currentProjectId);
        return project?.tasks.find(t => t.id === taskId);
    };

    const calculateProjectProgress = (project) => {
        if (!project || project.tasks.length === 0) return { percentage: 0, completed: 0, total: 0 };
        const total = project.tasks.length;
        const completed = project.tasks.filter(t => t.completed).length;
        return { percentage: Math.round((completed / total) * 100), completed, total };
    };

    const calculateTaskProgress = (task) => {
        if (!task.subtasks || task.subtasks.length === 0) return { completed: 0, total: 0 };
        const total = task.subtasks.length;
        const completed = task.subtasks.filter(st => st.completed).length;
        return { completed, total };
    };

    // --- AI & 外部服務整合 ---
    const addMessageToChat = (sender, message) => {
        const msgDiv = document.createElement('div');
        msgDiv.className = `text-sm p-2 rounded-lg ${sender === 'user' ? 'bg-blue-100 self-end' : 'bg-gray-200 self-start'}`;
        msgDiv.innerHTML = message;
        dom.chatHistory.appendChild(msgDiv);
        dom.chatHistory.scrollTop = dom.chatHistory.scrollHeight;
    };

    const callGeminiAPI = async (prompt) => {
        addMessageToChat('user', prompt);
        addMessageToChat('ai', '<i class="fas fa-spinner fa-spin"></i> 思考中...');

        const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${state.apiKey}`;
        const fullPrompt = `你是一個任務管理助理。根據使用者的要求，回傳一個 JSON 物件。JSON 必須包含 'action' (例如 'addTask', 'addProjectPlan') 和 'payload')。\n\n模式一：快速新增。若使用者指令單純，回傳單一動作 JSON。例如使用者說：「新增任務：買牛奶」，你回傳：{"action": "addTask", "payload": {"text": "買牛奶"}}。\n\n模式二：完整規劃。若使用者要求規劃，請回傳一個符合我們應用程式資料結構的完整專案物件 JSON。專案物件應包含 id, name, 和一個 tasks 陣列，tasks陣列中可包含subtasks。例如使用者說：「規劃一個為期一週的東京旅遊」，你回傳：{"action": "addProjectPlan", "payload": {"id": "proj-${Date.now()}", "name": "東京一週旅遊", "tasks": [{"id":"task-${Date.now() + 1}","text":"行前準備","completed":false,"expanded":true,"creationDate":"${new Date().toISOString()}","completionDate":null,"subtasks":[{"id":"sub-${Date.now() + 2}","text":"購買機票","completed":false,"creationDate":"${new Date().toISOString()}","completionDate":null}]}]}}\n\n使用者說：「${prompt}」`;

        try {
            const response = await fetch(API_URL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ contents: [{ parts: [{ text: fullPrompt }] }] })
            });

            dom.chatHistory.removeChild(dom.chatHistory.lastChild);

            if (!response.ok) {
                throw new Error(`API 請求失敗，狀態碼: ${response.status}`);
            }

            const data = await response.json();
            const aiResponseText = data.candidates[0].content.parts[0].text;
            const command = JSON.parse(aiResponseText.match(/\`\`\`json([\s\S]*?)\`\`\`/)[1]);
            executeAIAction(command);

        } catch (error) {
            console.error('Gemini API Error:', error);
            addMessageToChat('ai', '抱歉，AI 功能出現錯誤。請檢查您的 API 金鑰或網路連線。');
        }
    };

    const executeAIAction = (command) => {
        if (!command || !command.action) return;
        switch (command.action) {
            case 'addTask':
                if (state.currentProjectId) {
                    addTask(command.payload.text);
                    addMessageToChat('ai', `好的，已在目前專案新增任務：「${command.payload.text}」。`);
                } else {
                    addMessageToChat('ai', '請先選擇一個專案才能新增任務。');
                }
                break;
            case 'addProjectPlan':
                const newProject = { ...command.payload, id: `proj-${Date.now()}` };
                state.projects.push(newProject);
                state.currentProjectId = newProject.id;
                addMessageToChat('ai', `好的，已為您建立新專案：「${newProject.name}」。`);
                break;
            default:
                addMessageToChat('ai', '抱歉，我無法理解這個指令。');
        }
        render();
    };

    // --- 事件監聽器 ---
    const addEventListeners = () => {
        // 分頁切換
        dom.tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                state.activeTab = btn.dataset.tab;
                render();
            });
        });

        // 專案選擇
        dom.projectSelector.addEventListener('change', (e) => {
            state.currentProjectId = e.target.value;
            render();
        });

        // 新增專案
        dom.addProjectForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const name = dom.newProjectInput.value.trim();
            if (name) { addProject(name); dom.newProjectInput.value = ''; render(); }
        });

        // 新增任務
        dom.addTaskForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const text = dom.newTaskInput.value.trim();
            if (text) { addTask(text); dom.newTaskInput.value = ''; render(); }
        });

        // 任務卡片互動 (事件委派)
        const boardContent = dom.boardTabContent;
        boardContent.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (!action) return;

            const taskCard = e.target.closest('.task-card');
            if (!taskCard) return;
            const taskId = taskCard.dataset.taskId;

            if (action === 'toggle-complete') {
                toggleTaskProperty(taskId, 'completed');
            } else if (action === 'toggle-expand') {
                toggleTaskProperty(taskId, 'expanded');
            } else if (action === 'toggle-subtask-complete') {
                const subtaskEl = e.target.closest('[data-subtask-id]');
                if (subtaskEl) {
                    toggleSubtaskCompletion(taskId, subtaskEl.dataset.subtaskId);
                }
            }
            render();
        });

        boardContent.addEventListener('submit', (e) => {
            e.preventDefault();
            if (e.target.dataset.action === 'add-subtask') {
                const taskCard = e.target.closest('.task-card');
                const taskId = taskCard.dataset.taskId;
                const input = e.target.querySelector('input');
                const text = input.value.trim();
                if (text) { addSubtask(taskId, text); input.value = ''; render(); }
            }
        });

        // 設定頁
        dom.saveApiKeyBtn.addEventListener('click', () => {
            const key = dom.apiKeyInput.value.trim();
            state.apiKey = key ? key : null;
            render();
            alert(key ? 'API 金鑰已儲存。' : 'API 金鑰已清除。');
        });

        dom.exportJsonBtn.addEventListener('click', () => {
            const dataStr = JSON.stringify(state, null, 2);
            const blob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url; a.download = 'project-backup-v4.json'; a.click();
            URL.revokeObjectURL(url);
        });

        dom.importJsonInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = (event) => {
                try {
                    const importedState = JSON.parse(event.target.result);
                    if (importedState && importedState.projects && typeof importedState.apiKey !== 'undefined') {
                        state = importedState;
                        saveState();
                        alert('匯入成功！頁面將會重新整理。');
                        location.reload();
                    } else { alert('檔案格式不符！'); }
                } catch (err) { alert('讀取檔案失敗，請確認檔案為正確的 JSON 格式。'); console.error(err); }
            };
            reader.readAsText(file);
            e.target.value = '';
        });

        // AI 聊天
        dom.chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const userInput = dom.chatInput.value.trim();
            if (userInput && state.apiKey) {
                callGeminiAPI(userInput);
                dom.chatInput.value = '';
            }
        });
    };

    // --- 應用程式初始化 ---
    const init = () => {
        const savedState = loadState();
        if (savedState) {
            state = savedState;
        } else {
            // 建立一個預設的歡迎專案
            addProject('歡迎使用 V4！');
            state.currentProjectId = state.projects[0].id;
            addTask('點擊上方的「設定」來配置您的 API 金鑰');
            addTask('試著跟左邊的 AI 說：「規劃一個學習 Tailwind CSS 的計畫」');
        }
        addEventListeners();
        render();
    };

    init();
});