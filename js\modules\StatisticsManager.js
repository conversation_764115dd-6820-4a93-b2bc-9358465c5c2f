/**
 * StatisticsManager - V4.3
 * 統計分析管理器，處理數據統計和圖表生成
 */

class StatisticsManager {
    constructor(stateManager) {
        this.stateManager = stateManager;

        // 監聽狀態變化
        this.stateManager.subscribe((newState, oldState) => {
            if (newState.projects !== oldState.projects) {
                this.updateStatistics();
            }
        });
    }

    // 更新統計資料
    updateStatistics() {
        const stats = this.calculateStatistics();
        
        this.stateManager.setState({
            statistics: {
                ...this.stateManager.getState().statistics,
                ...stats,
                lastActiveDate: new Date().toISOString()
            }
        }, { silent: true });

        // 更新UI顯示
        this.updateStatisticsUI(stats);
    }

    // 計算統計資料
    calculateStatistics() {
        const state = this.stateManager.getState();
        const projects = state.projects;

        let totalTasks = 0;
        let completedTasks = 0;
        let totalProjects = projects.length;
        let completedProjects = 0;
        let totalCompletionTime = 0;
        let completedTasksWithTime = 0;

        const projectStats = [];
        const dailyStats = {};
        const weeklyStats = {};
        const monthlyStats = {};

        projects.forEach(project => {
            const projectTaskCount = project.tasks.length;
            const projectCompletedCount = project.tasks.filter(t => t.completed).length;

            // 使用新的進度計算邏輯：基於任務完成度的平均值
            let projectProgress = 0;
            if (projectTaskCount > 0) {
                const taskCompletionRates = project.tasks.map(task => {
                    if (!task.subtasks || task.subtasks.length === 0) {
                        return task.completed ? 100 : 0;
                    }
                    const subtaskTotal = task.subtasks.length;
                    const subtaskCompleted = task.subtasks.filter(st => st.completed).length;
                    return subtaskTotal === 0 ? 0 : (subtaskCompleted / subtaskTotal) * 100;
                });
                projectProgress = Math.round(taskCompletionRates.reduce((sum, rate) => sum + rate, 0) / taskCompletionRates.length);
            }

            totalTasks += projectTaskCount;
            completedTasks += projectCompletedCount;

            if (projectProgress === 100) {
                completedProjects++;
            }

            // 專案統計
            projectStats.push({
                id: project.id,
                name: project.name,
                totalTasks: projectTaskCount,
                completedTasks: projectCompletedCount,
                progress: projectProgress,
                creationDate: project.creationDate
            });

            // 任務完成時間統計
            project.tasks.forEach(task => {
                if (task.completed && task.creationDate && task.completionDate) {
                    const creationTime = new Date(task.creationDate).getTime();
                    const completionTime = new Date(task.completionDate).getTime();
                    const duration = completionTime - creationTime;
                    
                    if (duration > 0) {
                        totalCompletionTime += duration;
                        completedTasksWithTime++;
                    }

                    // 按日期統計
                    const completionDate = new Date(task.completionDate).toDateString();
                    dailyStats[completionDate] = (dailyStats[completionDate] || 0) + 1;

                    // 按週統計
                    const weekKey = this.getWeekKey(new Date(task.completionDate));
                    weeklyStats[weekKey] = (weeklyStats[weekKey] || 0) + 1;

                    // 按月統計
                    const monthKey = this.getMonthKey(new Date(task.completionDate));
                    monthlyStats[monthKey] = (monthlyStats[monthKey] || 0) + 1;
                }
            });
        });

        const averageCompletionTime = completedTasksWithTime > 0 ? 
            totalCompletionTime / completedTasksWithTime : 0;

        return {
            totalTasks,
            completedTasks,
            totalProjects,
            completedProjects,
            averageCompletionTime,
            projectStats,
            dailyStats,
            weeklyStats,
            monthlyStats,
            completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0,
            projectCompletionRate: totalProjects > 0 ? (completedProjects / totalProjects) * 100 : 0
        };
    }

    // 更新統計UI
    updateStatisticsUI(stats) {
        // 更新統計卡片
        this.updateStatCard('total-tasks-stat', stats.totalTasks);
        this.updateStatCard('completed-tasks-stat', stats.completedTasks);
        this.updateStatCard('total-projects-stat', stats.totalProjects);
        this.updateStatCard('avg-completion-time-stat', this.formatDuration(stats.averageCompletionTime));

        // 圖表功能已移除，只保留數值卡片
    }

    // 更新統計卡片
    updateStatCard(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }









    // 獲取週鍵值
    getWeekKey(date) {
        const year = date.getFullYear();
        const week = this.getWeekNumber(date);
        return `${year}-W${week}`;
    }

    // 獲取月鍵值
    getMonthKey(date) {
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        return `${year}-${month.toString().padStart(2, '0')}`;
    }

    // 獲取週數
    getWeekNumber(date) {
        const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
        const dayNum = d.getUTCDay() || 7;
        d.setUTCDate(d.getUTCDate() + 4 - dayNum);
        const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
        return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
    }

    // 格式化持續時間
    formatDuration(milliseconds) {
        if (!milliseconds || milliseconds <= 0) return '-';

        const days = Math.floor(milliseconds / (1000 * 60 * 60 * 24));
        const hours = Math.floor((milliseconds % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));

        if (days > 0) {
            return `${days}天${hours}小時`;
        } else if (hours > 0) {
            return `${hours}小時${minutes}分鐘`;
        } else {
            return `${minutes}分鐘`;
        }
    }

    // 獲取專案統計詳情
    getProjectStatistics() {
        const stats = this.calculateStatistics();
        return stats.projectStats;
    }

    // 獲取時間範圍統計
    getTimeRangeStatistics(startDate, endDate) {
        const state = this.stateManager.getState();
        const projects = state.projects;
        
        let completedInRange = 0;
        const start = new Date(startDate).getTime();
        const end = new Date(endDate).getTime();

        projects.forEach(project => {
            project.tasks.forEach(task => {
                if (task.completed && task.completionDate) {
                    const completionTime = new Date(task.completionDate).getTime();
                    if (completionTime >= start && completionTime <= end) {
                        completedInRange++;
                    }
                }
            });
        });

        return {
            completedTasks: completedInRange,
            startDate,
            endDate
        };
    }

    // 獲取效率統計
    getEfficiencyStatistics() {
        const now = new Date();
        const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

        const last7DaysStats = this.getTimeRangeStatistics(last7Days, now);
        const last30DaysStats = this.getTimeRangeStatistics(last30Days, now);

        return {
            last7Days: last7DaysStats.completedTasks,
            last30Days: last30DaysStats.completedTasks,
            dailyAverage: last30DaysStats.completedTasks / 30,
            weeklyAverage: last30DaysStats.completedTasks / 4.3
        };
    }



    // 匯出統計資料
    exportStatistics() {
        const stats = this.calculateStatistics();
        const efficiency = this.getEfficiencyStatistics();
        
        return {
            ...stats,
            efficiency,
            exportDate: new Date().toISOString()
        };
    }
}

// 導出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StatisticsManager;
} else {
    window.StatisticsManager = StatisticsManager;
}
