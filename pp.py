import http.server
import socketserver
import threading
import subprocess
import time
import webbrowser
import tkinter as tk
from tkinter import messagebox
import requests

PORT = 8080

# 🚫 禁用快取的 Handler
class NoCacheHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header("Cache-Control", "no-store, no-cache, must-revalidate")
        self.send_header("Pragma", "no-cache")
        self.send_header("Expires", "0")
        super().end_headers()

# 🌀 啟動 HTTP Server（非阻塞）
def start_server():
    with socketserver.TCPServer(("", PORT), NoCacheHandler) as httpd:
        print(f"Serving at http://localhost:{PORT}")
        httpd.serve_forever()

# 🌐 開瀏覽器
def open_browser():
    time.sleep(2)  # 等待 server 啟動
    webbrowser.open(f"http://localhost:{PORT}")

# 🧪 GUI 測試函式
def test_server():
    url = entry.get()
    try:
        response = requests.get(url)
        output.config(state='normal')
        output.delete(1.0, tk.END)
        output.insert(tk.END, f"狀態碼: {response.status_code}\n內容:\n{response.text[:500]}")
        output.config(state='disabled')
    except Exception as e:
        messagebox.showerror("錯誤", str(e))

# 🚀 啟動 Server 與瀏覽器
threading.Thread(target=start_server, daemon=True).start()
threading.Thread(target=open_browser, daemon=True).start()

# 🎨 建立 GUI 介面
window = tk.Tk()
window.title("HTTP 測試介面")
tk.Label(window, text="測試 URL：").pack(pady=5)
entry = tk.Entry(window, width=50)
entry.pack(pady=5)
entry.insert(0, f"http://localhost:{PORT}")
tk.Button(window, text="執行測試", command=test_server).pack(pady=10)
output = tk.Text(window, height=20, width=80, state='disabled')
output.pack(pady=5)
window.mainloop()
